# Fix SQLite references in all service files
Write-Host "Fixing SQLite references in all service files..."

$files = @(
    "Services\SupplierService.cs",
    "Services\CurrencyService.cs", 
    "Services\DebtService.cs"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        $content = $content -replace "using System\.Data\.SQLite;", "using Microsoft.Data.Sqlite;"
        $content = $content -replace "SQLiteConnection", "SqliteConnection"
        $content = $content -replace "SQLiteCommand", "SqliteCommand"
        
        Set-Content $file $content -NoNewline
        Write-Host "Fixed $file"
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "All SQLite references fixed!"
