# DebtManager - برنامج إدارة ديون العملاء والموردين

## وصف المشروع
برنامج مكتبي بلغة C# لإدارة ديون العملاء والموردين في محل تجاري (مثل الذهب أو الصرافة)، يسمح بتسجيل الديون بعملات متعددة (ليرة، دولار، ذهب، إلخ)، مع نظام تنبيهات لمواعيد السداد، وتصنيف الديون حسب اللون (أخضر لنا – أحمر علينا).

## المتطلبات التقنية
- **.NET 6.0** أو أحدث
- **Windows 10/11**
- **Visual Studio 2022** أو **Visual Studio Code**

## المكتبات المستخدمة
- **WPF** - واجهة المستخدم
- **SQLite** - قاعدة البيانات
- **Material Design** - التصميم العصري
- **ClosedXML** - تصدير Excel
- **iTextSharp** - تصدير PDF

## كيفية التشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd DebtManager
```

### 2. استعادة الحزم
```bash
dotnet restore
```

### 3. بناء المشروع
```bash
dotnet build
```

### 4. تشغيل المشروع
```bash
dotnet run
```

أو من خلال Visual Studio:
- افتح ملف `DebtManager.csproj`
- اضغط F5 للتشغيل

## الميزات الرئيسية

### 🏠 الشاشة الرئيسية (Dashboard)
- ملخص ديون الموردين والعملاء
- عرض مجموع الذهب والعملات
- تقسيم واضح: ديون لنا (بالأخضر) / ديون علينا (بالأحمر)
- عرض تنبيهات لمواعيد السداد القادمة
- آخر المعاملات

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- البحث في قائمة العملاء
- عرض تفاصيل ديون كل عميل
- حساب الرصيد الصافي لكل عميل

### 🚚 إدارة الموردين
- إضافة وتعديل وحذف الموردين
- البحث في قائمة الموردين
- عرض تفاصيل ديون كل مورد
- حساب الرصيد الصافي لكل مورد

### 💰 إدارة المعاملات المالية
- إضافة معاملات سحب ودفع
- دعم عملات متعددة
- تحديد تواريخ الاستحقاق
- إضافة ملاحظات للمعاملات
- تسديد الديون

### 🪙 إدارة العملات
- إضافة عملات جديدة
- تحديد العملات التي تمثل الذهب
- تعديل وحذف العملات

### 🔔 نظام التنبيهات
- تنبيه تلقائي للديون المتأخرة
- عرض الديون المستحقة قريباً
- إشعارات مرئية في الواجهة

## هيكل المشروع

```
DebtManager/
├── Models/                 # نماذج البيانات
│   ├── Client.cs          # نموذج العميل
│   ├── Supplier.cs        # نموذج المورد
│   ├── Currency.cs        # نموذج العملة
│   └── Debt.cs           # نموذج الدين
├── Services/              # خدمات البيانات
│   ├── ClientService.cs   # خدمة العملاء
│   ├── SupplierService.cs # خدمة الموردين
│   ├── CurrencyService.cs # خدمة العملات
│   └── DebtService.cs    # خدمة الديون
├── Data/                  # قاعدة البيانات
│   └── DatabaseService.cs # خدمة قاعدة البيانات
├── Views/                 # واجهات المستخدم
│   ├── MainWindow.xaml    # الشاشة الرئيسية
│   ├── ClientsWindow.xaml # نافذة العملاء
│   ├── SuppliersWindow.xaml # نافذة الموردين
│   ├── DebtWindow.xaml    # نافذة المعاملات
│   └── CurrenciesWindow.xaml # نافذة العملات
├── App.xaml              # تطبيق WPF
└── DebtManager.csproj    # ملف المشروع
```

## قاعدة البيانات

يستخدم المشروع قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:
- **Clients** - بيانات العملاء
- **Suppliers** - بيانات الموردين  
- **Currencies** - العملات المتاحة
- **Debts** - المعاملات المالية

يتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل للبرنامج.

## العملات الافتراضية

يتم إضافة العملات التالية تلقائياً:
- الليرة السورية (SYP)
- الدولار الأمريكي (USD)
- اليورو (EUR)
- الذهب (GOLD)
- الليرة التركية (TRY)

## الاستخدام

### إضافة عميل جديد
1. اذهب إلى "العملاء"
2. اضغط "إضافة عميل"
3. املأ البيانات المطلوبة
4. اضغط "حفظ"

### إضافة معاملة مالية
1. اضغط "إضافة دين" من الشاشة الرئيسية
2. اختر نوع الشخص (عميل/مورد)
3. اختر الشخص من القائمة
4. اختر نوع المعاملة (سحب/دفع)
5. أدخل المبلغ والعملة
6. حدد التواريخ والملاحظات
7. اضغط "حفظ"

### عرض ديون شخص معين
1. اذهب إلى "العملاء" أو "الموردين"
2. اختر الشخص من القائمة
3. اضغط "عرض الديون"

## المطور
تم تطوير هذا البرنامج باستخدام:
- **C#** و **WPF**
- **Material Design** للتصميم
- **SQLite** لقاعدة البيانات

## الترخيص
هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
