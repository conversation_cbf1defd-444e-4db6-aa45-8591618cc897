# Fix using statements in all files
Write-Host "Adding missing using statements..."

# Service files
$serviceFiles = @(
    "Services\SupplierService.cs",
    "Services\CurrencyService.cs", 
    "Services\DebtService.cs"
)

foreach ($file in $serviceFiles) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        
        # Add missing using statements after the first using line
        if ($content -notmatch "using System\.Collections\.Generic;") {
            $content = $content -replace "(using Microsoft\.Data\.Sqlite;)", "`$1`nusing System.Collections.Generic;`nusing System.Threading.Tasks;"
        }
        
        # Fix SQLiteDataReader
        $content = $content -replace "SQLiteDataReader", "SqliteDataReader"
        
        Set-Content $file $content -NoNewline
        Write-Host "Fixed $file"
    }
}

# View files
$viewFiles = @(
    "Views\ClientDebtsWindow.xaml.cs",
    "Views\ClientsWindow.xaml.cs",
    "Views\CurrenciesWindow.xaml.cs",
    "Views\CurrencyDialog.xaml.cs",
    "Views\DebtWindow.xaml.cs",
    "Views\MainWindow.xaml.cs",
    "Views\SupplierDebtsWindow.xaml.cs",
    "Views\SuppliersWindow.xaml.cs"
)

foreach ($file in $viewFiles) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        
        # Add missing using statements at the top
        if ($content -notmatch "using System\.Collections\.Generic;") {
            $content = $content -replace "(using System\.Windows;)", "using System.Collections.Generic;`nusing System.Threading.Tasks;`n`$1"
        }
        
        Set-Content $file $content -NoNewline
        Write-Host "Fixed $file"
    }
}

Write-Host "All using statements fixed!"
