using Microsoft.Data.Sqlite;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DebtManager.Data;
using DebtManager.Models;

namespace DebtManager.Services
{
    public class DebtService
    {
        public async Task<List<Debt>> GetAllDebtsAsync()
        {
            var debts = new List<Debt>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT d.Id, d.ClientId, d.Supplier<PERSON>d, d.<PERSON>urrencyId, d.Amount, 
                                d.DebtType, d.TransactionType, d.TransactionDate, d.DueDate,
                                d.<PERSON>, d.<PERSON>, d.Is<PERSON>aid, d.Paid<PERSON>ate, d.<PERSON>ate, d.<PERSON>y,
                                c.Name as ClientName, s.Name as SupplierName, 
                                cur.Name as CurrencyName, cur.Symbol as CurrencySymbol
                         FROM Debts d
                         LEFT JOIN Clients c ON d.ClientId = c.Id
                         LEFT JOIN Suppliers s ON d.SupplierId = s.Id
                         INNER JOIN Currencies cur ON d.CurrencyId = cur.Id
                         WHERE d.IsActive = 1
                         ORDER BY d.CreatedDate DESC";
            
            using var command = new SqliteCommand(query, connection);
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                var debt = new Debt
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    ClientId = reader.IsDBNull(reader.GetOrdinal("ClientId")) ? null : reader.GetInt32(reader.GetOrdinal("ClientId")),
                    SupplierId = reader.IsDBNull(reader.GetOrdinal("SupplierId")) ? null : reader.GetInt32(reader.GetOrdinal("SupplierId")),
                    CurrencyId = reader.GetInt32(reader.GetOrdinal("CurrencyId")),
                    Amount = reader.GetDecimal(reader.GetOrdinal("Amount")),
                    DebtType = (DebtType)reader.GetInt32(reader.GetOrdinal("DebtType")),
                    TransactionType = (TransactionType)reader.GetInt32(reader.GetOrdinal("TransactionType")),
                    TransactionDate = reader.GetDateTime(reader.GetOrdinal("TransactionDate")),
                    DueDate = reader.IsDBNull(reader.GetOrdinal("DueDate")) ? null : reader.GetDateTime(reader.GetOrdinal("DueDate")),
                    Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    IsPaid = reader.GetBoolean(reader.GetOrdinal("IsPaid")),
                    PaidDate = reader.IsDBNull(reader.GetOrdinal("PaidDate")) ? null : reader.GetDateTime(reader.GetOrdinal("PaidDate")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate")),
                    CreatedBy = reader.IsDBNull(reader.GetOrdinal("CreatedBy")) ? null : reader.GetString(reader.GetOrdinal("CreatedBy"))
                };

                // Create related objects
                if (debt.ClientId.HasValue)
                {
                    debt.Client = new Client
                    {
                        Id = debt.ClientId.Value,
                        Name = reader.GetString(reader.GetOrdinal("ClientName"))
                    };
                }

                if (debt.SupplierId.HasValue)
                {
                    debt.Supplier = new Supplier
                    {
                        Id = debt.SupplierId.Value,
                        Name = reader.GetString(reader.GetOrdinal("SupplierName"))
                    };
                }

                debt.Currency = new Currency
                {
                    Id = debt.CurrencyId,
                    Name = reader.GetString(reader.GetOrdinal("CurrencyName")),
                    Symbol = reader.GetString(reader.GetOrdinal("CurrencySymbol"))
                };

                debts.Add(debt);
            }
            
            return debts;
        }

        public async Task<List<Debt>> GetDebtsByClientIdAsync(int clientId)
        {
            var debts = new List<Debt>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT d.Id, d.ClientId, d.SupplierId, d.CurrencyId, d.Amount, 
                                d.DebtType, d.TransactionType, d.TransactionDate, d.DueDate,
                                d.Notes, d.IsActive, d.IsPaid, d.PaidDate, d.CreatedDate, d.CreatedBy,
                                c.Name as ClientName, cur.Name as CurrencyName, cur.Symbol as CurrencySymbol
                         FROM Debts d
                         INNER JOIN Clients c ON d.ClientId = c.Id
                         INNER JOIN Currencies cur ON d.CurrencyId = cur.Id
                         WHERE d.ClientId = @clientId AND d.IsActive = 1
                         ORDER BY d.CreatedDate DESC";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@clientId", clientId);
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                var debt = CreateDebtFromReader(reader);
                debts.Add(debt);
            }
            
            return debts;
        }

        public async Task<List<Debt>> GetDebtsBySupplierIdAsync(int supplierId)
        {
            var debts = new List<Debt>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT d.Id, d.ClientId, d.SupplierId, d.CurrencyId, d.Amount, 
                                d.DebtType, d.TransactionType, d.TransactionDate, d.DueDate,
                                d.Notes, d.IsActive, d.IsPaid, d.PaidDate, d.CreatedDate, d.CreatedBy,
                                s.Name as SupplierName, cur.Name as CurrencyName, cur.Symbol as CurrencySymbol
                         FROM Debts d
                         INNER JOIN Suppliers s ON d.SupplierId = s.Id
                         INNER JOIN Currencies cur ON d.CurrencyId = cur.Id
                         WHERE d.SupplierId = @supplierId AND d.IsActive = 1
                         ORDER BY d.CreatedDate DESC";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@supplierId", supplierId);
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                var debt = CreateDebtFromReader(reader);
                debts.Add(debt);
            }
            
            return debts;
        }

        public async Task<int> AddDebtAsync(Debt debt)
        {
            try
            {
                using var connection = new SqliteConnection(DatabaseService.ConnectionString);
                await connection.OpenAsync();

                var query = @"INSERT INTO Debts (ClientId, SupplierId, CurrencyId, Amount, DebtType,
                                               TransactionType, TransactionDate, DueDate, Notes,
                                               IsActive, IsPaid, CreatedDate, CreatedBy)
                             VALUES (@clientId, @supplierId, @currencyId, @amount, @debtType,
                                    @transactionType, @transactionDate, @dueDate, @notes,
                                    @isActive, @isPaid, @createdDate, @createdBy);
                             SELECT last_insert_rowid();";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@clientId", debt.ClientId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@supplierId", debt.SupplierId ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@currencyId", debt.CurrencyId);
                command.Parameters.AddWithValue("@amount", debt.Amount);
                command.Parameters.AddWithValue("@debtType", (int)debt.DebtType);
                command.Parameters.AddWithValue("@transactionType", (int)debt.TransactionType);
                command.Parameters.AddWithValue("@transactionDate", debt.TransactionDate);
                command.Parameters.AddWithValue("@dueDate", debt.DueDate ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@notes", debt.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@isActive", debt.IsActive);
                command.Parameters.AddWithValue("@isPaid", debt.IsPaid);
                command.Parameters.AddWithValue("@createdDate", debt.CreatedDate);
                command.Parameters.AddWithValue("@createdBy", debt.CreatedBy ?? (object)DBNull.Value);

                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result ?? 0);
            }
            catch (Exception ex)
            {
                // Log the error (in a real app, use proper logging)
                System.Diagnostics.Debug.WriteLine($"Error adding debt: {ex.Message}");
                throw; // Re-throw to let the UI handle it
            }
        }

        public async Task<bool> MarkDebtAsPaidAsync(int debtId)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = "UPDATE Debts SET IsPaid = 1, PaidDate = @paidDate WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", debtId);
            command.Parameters.AddWithValue("@paidDate", DateTime.Now);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        public async Task<List<Debt>> GetOverdueDebtsAsync()
        {
            var debts = new List<Debt>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT d.Id, d.ClientId, d.SupplierId, d.CurrencyId, d.Amount, 
                                d.DebtType, d.TransactionType, d.TransactionDate, d.DueDate,
                                d.Notes, d.IsActive, d.IsPaid, d.PaidDate, d.CreatedDate, d.CreatedBy,
                                COALESCE(c.Name, s.Name) as PersonName, 
                                cur.Name as CurrencyName, cur.Symbol as CurrencySymbol
                         FROM Debts d
                         LEFT JOIN Clients c ON d.ClientId = c.Id
                         LEFT JOIN Suppliers s ON d.SupplierId = s.Id
                         INNER JOIN Currencies cur ON d.CurrencyId = cur.Id
                         WHERE d.IsActive = 1 AND d.IsPaid = 0 AND d.DueDate < @today
                         ORDER BY d.DueDate ASC";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@today", DateTime.Today);
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                var debt = CreateDebtFromReader(reader);
                debts.Add(debt);
            }
            
            return debts;
        }

        private Debt CreateDebtFromReader(SqliteDataReader reader)
        {
            var debt = new Debt
            {
                Id = reader.GetInt32("Id"),
                ClientId = reader.IsDBNull("ClientId") ? null : reader.GetInt32("ClientId"),
                SupplierId = reader.IsDBNull("SupplierId") ? null : reader.GetInt32("SupplierId"),
                CurrencyId = reader.GetInt32("CurrencyId"),
                Amount = reader.GetDecimal("Amount"),
                DebtType = (DebtType)reader.GetInt32("DebtType"),
                TransactionType = (TransactionType)reader.GetInt32("TransactionType"),
                TransactionDate = reader.GetDateTime("TransactionDate"),
                DueDate = reader.IsDBNull("DueDate") ? null : reader.GetDateTime("DueDate"),
                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                IsActive = reader.GetBoolean("IsActive"),
                IsPaid = reader.GetBoolean("IsPaid"),
                PaidDate = reader.IsDBNull("PaidDate") ? null : reader.GetDateTime("PaidDate"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                CreatedBy = reader.IsDBNull("CreatedBy") ? null : reader.GetString("CreatedBy")
            };

            debt.Currency = new Currency
            {
                Id = debt.CurrencyId,
                Name = reader.GetString("CurrencyName"),
                Symbol = reader.GetString("CurrencySymbol")
            };

            return debt;
        }
    }
}
