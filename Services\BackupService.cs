using System;
using System.IO;
using System.Threading.Tasks;
using System.Text.Json;
using System.Collections.Generic;
using System.Linq;
using DebtManager.Models;
using Microsoft.Win32;
using System.Windows;

namespace DebtManager.Services
{
    public class BackupService
    {
        private readonly DebtService _debtService;
        private readonly ClientService _clientService;
        private readonly SupplierService _supplierService;
        private readonly CurrencyService _currencyService;

        public BackupService()
        {
            _debtService = new DebtService();
            _clientService = new ClientService();
            _supplierService = new SupplierService();
            _currencyService = new CurrencyService();
        }

        public async Task<bool> CreateBackupAsync(string filePath)
        {
            try
            {
                var backupData = new BackupData
                {
                    CreatedDate = DateTime.Now,
                    Version = "1.0",
                    Clients = await _clientService.GetAllClientsAsync(),
                    Suppliers = await _supplierService.GetAllSuppliersAsync(),
                    Currencies = await _currencyService.GetAllCurrenciesAsync(),
                    Debts = await _debtService.GetAllDebtsAsync()
                };

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(backupData, options);
                await File.WriteAllTextAsync(filePath, json, System.Text.Encoding.UTF8);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Backup creation failed: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> RestoreBackupAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                var json = await File.ReadAllTextAsync(filePath, System.Text.Encoding.UTF8);
                var backupData = JsonSerializer.Deserialize<BackupData>(json);

                if (backupData == null)
                    return false;

                // Confirm with user before restoring
                var result = MessageBox.Show(
                    $"هل تريد استعادة النسخة الاحتياطية؟\n\nتاريخ النسخة: {backupData.CreatedDate:dd/MM/yyyy HH:mm}\nالإصدار: {backupData.Version}\n\nتحذير: سيتم حذف جميع البيانات الحالية!",
                    "تأكيد الاستعادة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                    return false;

                // Clear existing data (in a real app, you'd want to do this in a transaction)
                await ClearAllDataAsync();

                // Restore currencies first (they're referenced by debts)
                foreach (var currency in backupData.Currencies)
                {
                    currency.Id = 0; // Reset ID for new insertion
                    await _currencyService.AddCurrencyAsync(currency);
                }

                // Restore clients
                foreach (var client in backupData.Clients)
                {
                    client.Id = 0; // Reset ID for new insertion
                    await _clientService.AddClientAsync(client);
                }

                // Restore suppliers
                foreach (var supplier in backupData.Suppliers)
                {
                    supplier.Id = 0; // Reset ID for new insertion
                    await _supplierService.AddSupplierAsync(supplier);
                }

                // Restore debts (this is more complex due to foreign key relationships)
                var newCurrencies = await _currencyService.GetAllCurrenciesAsync();
                var newClients = await _clientService.GetAllClientsAsync();
                var newSuppliers = await _supplierService.GetAllSuppliersAsync();

                foreach (var debt in backupData.Debts)
                {
                    debt.Id = 0; // Reset ID for new insertion

                    // Map currency by name
                    var originalCurrency = backupData.Currencies.FirstOrDefault(c => c.Id == debt.CurrencyId);
                    if (originalCurrency != null)
                    {
                        var newCurrency = newCurrencies.FirstOrDefault(c => c.Name == originalCurrency.Name);
                        if (newCurrency != null)
                            debt.CurrencyId = newCurrency.Id;
                    }

                    // Map client by name if applicable
                    if (debt.ClientId.HasValue)
                    {
                        var originalClient = backupData.Clients.FirstOrDefault(c => c.Id == debt.ClientId.Value);
                        if (originalClient != null)
                        {
                            var newClient = newClients.FirstOrDefault(c => c.Name == originalClient.Name);
                            if (newClient != null)
                                debt.ClientId = newClient.Id;
                            else
                                debt.ClientId = null;
                        }
                    }

                    // Map supplier by name if applicable
                    if (debt.SupplierId.HasValue)
                    {
                        var originalSupplier = backupData.Suppliers.FirstOrDefault(s => s.Id == debt.SupplierId.Value);
                        if (originalSupplier != null)
                        {
                            var newSupplier = newSuppliers.FirstOrDefault(s => s.Name == originalSupplier.Name);
                            if (newSupplier != null)
                                debt.SupplierId = newSupplier.Id;
                            else
                                debt.SupplierId = null;
                        }
                    }

                    await _debtService.AddDebtAsync(debt);
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Backup restoration failed: {ex.Message}");
                return false;
            }
        }

        private async Task ClearAllDataAsync()
        {
            // Note: In a real application, you'd want to implement proper cascade delete
            // or use database transactions. This is a simplified version.
            
            try
            {
                var allDebts = await _debtService.GetAllDebtsAsync();
                foreach (var debt in allDebts)
                {
                    await _debtService.DeleteDebtAsync(debt.Id);
                }

                var allClients = await _clientService.GetAllClientsAsync();
                foreach (var client in allClients)
                {
                    await _clientService.DeleteClientAsync(client.Id);
                }

                var allSuppliers = await _supplierService.GetAllSuppliersAsync();
                foreach (var supplier in allSuppliers)
                {
                    await _supplierService.DeleteSupplierAsync(supplier.Id);
                }

                // Don't delete currencies as they might be system defaults
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Clear data failed: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> CreateAutoBackupAsync()
        {
            try
            {
                var backupFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "DebtManager", "Backups");
                Directory.CreateDirectory(backupFolder);

                var fileName = $"AutoBackup_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.json";
                var filePath = Path.Combine(backupFolder, fileName);

                var success = await CreateBackupAsync(filePath);

                if (success)
                {
                    // Keep only the last 10 auto backups
                    await CleanupOldBackupsAsync(backupFolder);
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Auto backup failed: {ex.Message}");
                return false;
            }
        }

        private async Task CleanupOldBackupsAsync(string backupFolder)
        {
            try
            {
                var backupFiles = Directory.GetFiles(backupFolder, "AutoBackup_*.json")
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .Skip(10) // Keep the 10 most recent
                    .ToList();

                foreach (var file in backupFiles)
                {
                    file.Delete();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Cleanup old backups failed: {ex.Message}");
            }
        }

        public string GetBackupInfo(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return "الملف غير موجود";

                var json = File.ReadAllText(filePath, System.Text.Encoding.UTF8);
                var backupData = JsonSerializer.Deserialize<BackupData>(json);

                if (backupData == null)
                    return "ملف نسخة احتياطية غير صالح";

                return $"تاريخ النسخة: {backupData.CreatedDate:dd/MM/yyyy HH:mm}\n" +
                       $"الإصدار: {backupData.Version}\n" +
                       $"العملاء: {backupData.Clients.Count}\n" +
                       $"الموردين: {backupData.Suppliers.Count}\n" +
                       $"العملات: {backupData.Currencies.Count}\n" +
                       $"المعاملات: {backupData.Debts.Count}";
            }
            catch
            {
                return "خطأ في قراءة معلومات النسخة الاحتياطية";
            }
        }
    }

    public class BackupData
    {
        public DateTime CreatedDate { get; set; }
        public string Version { get; set; } = string.Empty;
        public List<Client> Clients { get; set; } = new();
        public List<Supplier> Suppliers { get; set; } = new();
        public List<Currency> Currencies { get; set; } = new();
        public List<Debt> Debts { get; set; } = new();
    }
}
