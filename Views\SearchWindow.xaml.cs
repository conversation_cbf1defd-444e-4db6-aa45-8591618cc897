using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Text.RegularExpressions;
using DebtManager.Services;
using DebtManager.Models;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace DebtManager.Views
{
    public partial class SearchWindow : Window
    {
        private readonly DebtService _debtService;
        private readonly CurrencyService _currencyService;
        private List<Debt> _allDebts = new();
        private List<Currency> _currencies = new();
        private List<Debt> _searchResults = new();

        public SearchWindow()
        {
            InitializeComponent();
            _debtService = new DebtService();
            _currencyService = new CurrencyService();
        }

        private async void SearchWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
            InitializeFilters();
            await PerformSearchAsync(); // Load all data initially
        }

        private async Task LoadDataAsync()
        {
            try
            {
                _allDebts = await _debtService.GetAllDebtsAsync();
                _currencies = await _currencyService.GetAllCurrenciesAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeFilters()
        {
            // Setup currency filter
            var allCurrenciesOption = new Currency { Id = 0, Name = "جميع العملات" };
            var currencyOptions = new List<Currency> { allCurrenciesOption };
            currencyOptions.AddRange(_currencies);
            
            CurrencyComboBox.ItemsSource = currencyOptions;
            CurrencyComboBox.SelectedIndex = 0;

            // Set default date range (last 3 months)
            DateToPicker.SelectedDate = DateTime.Today;
            DateFromPicker.SelectedDate = DateTime.Today.AddMonths(-3);
        }

        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformSearchAsync();
        }

        private async void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Auto-search after 500ms delay
            await Task.Delay(500);
            if (SearchTextBox.Text == ((TextBox)sender).Text) // Check if text hasn't changed
            {
                await PerformSearchAsync();
            }
        }

        private async Task PerformSearchAsync()
        {
            try
            {
                _searchResults = ApplyFilters();
                ResultsDataGrid.ItemsSource = _searchResults.OrderByDescending(d => d.TransactionDate);
                UpdateResultsCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<Debt> ApplyFilters()
        {
            var filtered = _allDebts.AsEnumerable();

            // Text search
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchText = SearchTextBox.Text.Trim().ToLower();
                filtered = filtered.Where(d => 
                    d.PersonName.ToLower().Contains(searchText) ||
                    (d.Notes != null && d.Notes.ToLower().Contains(searchText)));
            }

            // Amount range filter
            if (!string.IsNullOrWhiteSpace(AmountFromTextBox.Text) && decimal.TryParse(AmountFromTextBox.Text, out decimal amountFrom))
            {
                filtered = filtered.Where(d => d.Amount >= amountFrom);
            }

            if (!string.IsNullOrWhiteSpace(AmountToTextBox.Text) && decimal.TryParse(AmountToTextBox.Text, out decimal amountTo))
            {
                filtered = filtered.Where(d => d.Amount <= amountTo);
            }

            // Date range filter
            if (DateFromPicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(d => d.TransactionDate.Date >= DateFromPicker.SelectedDate.Value.Date);
            }

            if (DateToPicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(d => d.TransactionDate.Date <= DateToPicker.SelectedDate.Value.Date);
            }

            // Currency filter
            var selectedCurrency = CurrencyComboBox.SelectedItem as Currency;
            if (selectedCurrency != null && selectedCurrency.Id > 0)
            {
                filtered = filtered.Where(d => d.CurrencyId == selectedCurrency.Id);
            }

            // Debt type filter
            var selectedDebtType = ((ComboBoxItem)DebtTypeComboBox.SelectedItem).Tag.ToString();
            if (selectedDebtType != "All")
            {
                if (Enum.TryParse<DebtType>(selectedDebtType, out DebtType debtType))
                {
                    filtered = filtered.Where(d => d.DebtType == debtType);
                }
            }

            // Status filter
            var selectedStatus = ((ComboBoxItem)StatusComboBox.SelectedItem).Tag.ToString();
            if (selectedStatus != "All")
            {
                filtered = selectedStatus switch
                {
                    "Active" => filtered.Where(d => !d.IsPaid && !d.IsOverdue && d.DaysUntilDue > 7),
                    "Paid" => filtered.Where(d => d.IsPaid),
                    "Overdue" => filtered.Where(d => d.IsOverdue),
                    "DueSoon" => filtered.Where(d => !d.IsPaid && !d.IsOverdue && d.DaysUntilDue <= 7 && d.DaysUntilDue >= 0),
                    _ => filtered
                };
            }

            // Person type filter
            var selectedPersonType = ((ComboBoxItem)PersonTypeComboBox.SelectedItem).Tag.ToString();
            if (selectedPersonType != "All")
            {
                filtered = selectedPersonType switch
                {
                    "Clients" => filtered.Where(d => d.ClientId.HasValue),
                    "Suppliers" => filtered.Where(d => d.SupplierId.HasValue),
                    _ => filtered
                };
            }

            return filtered.ToList();
        }

        private void UpdateResultsCount()
        {
            var count = _searchResults.Count;
            ResultsCountText.Text = count == 1 ? "(نتيجة واحدة)" : $"({count} نتيجة)";
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            // Clear all filters
            SearchTextBox.Text = "";
            AmountFromTextBox.Text = "";
            AmountToTextBox.Text = "";
            DateFromPicker.SelectedDate = DateTime.Today.AddMonths(-3);
            DateToPicker.SelectedDate = DateTime.Today;
            CurrencyComboBox.SelectedIndex = 0;
            DebtTypeComboBox.SelectedIndex = 0;
            StatusComboBox.SelectedIndex = 0;
            PersonTypeComboBox.SelectedIndex = 0;

            // Refresh search
            _ = PerformSearchAsync();
        }

        private void SaveFilterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Search Filter files (*.filter)|*.filter",
                    DefaultExt = "filter",
                    FileName = $"فلتر_البحث_{DateTime.Now:yyyy-MM-dd}.filter"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    SaveCurrentFilter(saveFileDialog.FileName);
                    MessageBox.Show("تم حفظ الفلتر بنجاح!", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفلتر: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveCurrentFilter(string fileName)
        {
            var filterData = new StringBuilder();
            filterData.AppendLine($"SearchText={SearchTextBox.Text}");
            filterData.AppendLine($"AmountFrom={AmountFromTextBox.Text}");
            filterData.AppendLine($"AmountTo={AmountToTextBox.Text}");
            filterData.AppendLine($"DateFrom={DateFromPicker.SelectedDate?.ToString("yyyy-MM-dd")}");
            filterData.AppendLine($"DateTo={DateToPicker.SelectedDate?.ToString("yyyy-MM-dd")}");
            filterData.AppendLine($"Currency={((Currency)CurrencyComboBox.SelectedItem)?.Id}");
            filterData.AppendLine($"DebtType={((ComboBoxItem)DebtTypeComboBox.SelectedItem).Tag}");
            filterData.AppendLine($"Status={((ComboBoxItem)StatusComboBox.SelectedItem).Tag}");
            filterData.AppendLine($"PersonType={((ComboBoxItem)PersonTypeComboBox.SelectedItem).Tag}");

            File.WriteAllText(fileName, filterData.ToString(), Encoding.UTF8);
        }

        private void ExportResultsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv",
                    DefaultExt = "csv",
                    FileName = $"نتائج_البحث_{DateTime.Now:yyyy-MM-dd}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportResultsToCsv(saveFileDialog.FileName);
                    MessageBox.Show("تم تصدير النتائج بنجاح!", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير النتائج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportResultsToCsv(string fileName)
        {
            var content = new StringBuilder();
            
            // Header
            content.AppendLine("التاريخ والوقت,الشخص,المبلغ,العملة,النوع,العملية,الحالة,الملاحظات");
            
            // Data
            foreach (var debt in _searchResults)
            {
                content.AppendLine($"{debt.TransactionDate:dd/MM/yyyy HH:mm},{debt.PersonName},{debt.Amount:N2},{debt.Currency.Symbol},{debt.DebtTypeText},{debt.TransactionTypeText},{debt.StatusText},\"{debt.Notes}\"");
            }

            File.WriteAllText(fileName, content.ToString(), Encoding.UTF8);
        }

        private void ResultsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ResultsDataGrid.SelectedItem is Debt selectedDebt)
            {
                // Open debt details (could open edit window)
                MessageBox.Show($"تفاصيل المعاملة:\n\nالشخص: {selectedDebt.PersonName}\nالمبلغ: {selectedDebt.Amount:N2} {selectedDebt.Currency.Symbol}\nالتاريخ: {selectedDebt.TransactionDate:dd/MM/yyyy HH:mm}\nالملاحظات: {selectedDebt.Notes}", 
                    "تفاصيل المعاملة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void AmountTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            Regex regex = new Regex("[^0-9.]+");
            e.Handled = regex.IsMatch(e.Text);
        }
    }
}
