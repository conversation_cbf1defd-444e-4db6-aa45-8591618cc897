﻿#pragma checksum "..\..\..\..\Views\ClientsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2DC5D32A2DEC0EB11118986EE859F3743FABB550"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Views {
    
    
    /// <summary>
    /// ClientsWindow
    /// </summary>
    public partial class ClientsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\..\Views\ClientsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Views\ClientsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddClientButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\ClientsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditClientButton;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\ClientsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewDebtsButton;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\ClientsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteClientButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\ClientsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ClientsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/views/clientswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ClientsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 45 "..\..\..\..\Views\ClientsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BackButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 68 "..\..\..\..\Views\ClientsWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AddClientButton = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\Views\ClientsWindow.xaml"
            this.AddClientButton.Click += new System.Windows.RoutedEventHandler(this.AddClientButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.EditClientButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\ClientsWindow.xaml"
            this.EditClientButton.Click += new System.Windows.RoutedEventHandler(this.EditClientButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ViewDebtsButton = ((System.Windows.Controls.Button)(target));
            
            #line 93 "..\..\..\..\Views\ClientsWindow.xaml"
            this.ViewDebtsButton.Click += new System.Windows.RoutedEventHandler(this.ViewDebtsButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DeleteClientButton = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\Views\ClientsWindow.xaml"
            this.DeleteClientButton.Click += new System.Windows.RoutedEventHandler(this.DeleteClientButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ClientsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 132 "..\..\..\..\Views\ClientsWindow.xaml"
            this.ClientsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ClientsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 133 "..\..\..\..\Views\ClientsWindow.xaml"
            this.ClientsDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.ClientsDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

