﻿#pragma checksum "..\..\..\..\Views\ReportsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BCBE4B32AD7558CECAE66F7047F8834AF596A55A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Views {
    
    
    /// <summary>
    /// ReportsWindow
    /// </summary>
    public partial class ReportsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateReportButton;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDebtsToUsText;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalOwedByUsText;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NetBalanceText;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverdueDebtsText;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SummaryByCurrencyGrid;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DetailedReportGrid;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportPdfButton;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportExcelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/views/reportswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ReportsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 14 "..\..\..\..\Views\ReportsWindow.xaml"
            ((DebtManager.Views.ReportsWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.ReportsWindow_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 3:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.CurrencyFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.GenerateReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\Views\ReportsWindow.xaml"
            this.GenerateReportButton.Click += new System.Windows.RoutedEventHandler(this.GenerateReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TotalDebtsToUsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TotalOwedByUsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.NetBalanceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.OverdueDebtsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SummaryByCurrencyGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 11:
            this.DetailedReportGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            this.ExportPdfButton = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\Views\ReportsWindow.xaml"
            this.ExportPdfButton.Click += new System.Windows.RoutedEventHandler(this.ExportPdfButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ExportExcelButton = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\Views\ReportsWindow.xaml"
            this.ExportExcelButton.Click += new System.Windows.RoutedEventHandler(this.ExportExcelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

