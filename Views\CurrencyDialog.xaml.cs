using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class CurrencyDialog : Window
    {
        private readonly CurrencyService _currencyService;
        private readonly Currency? _existingCurrency;
        private readonly bool _isEditMode;

        public CurrencyDialog(Currency? currency = null)
        {
            InitializeComponent();
            _currencyService = new CurrencyService();
            _existingCurrency = currency;
            _isEditMode = currency != null;
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            if (_isEditMode && _existingCurrency != null)
            {
                HeaderText.Text = "تعديل بيانات العملة";
                Title = "تعديل عملة";
                
                // Fill form with existing data
                NameTextBox.Text = _existingCurrency.Name;
                CodeTextBox.Text = _existingCurrency.Code;
                SymbolTextBox.Text = _existingCurrency.Symbol;
                IsGoldCheckBox.IsChecked = _existingCurrency.IsGold;
            }
            else
            {
                HeaderText.Text = "إضافة عملة جديدة";
                Title = "إضافة عملة";
            }
            
            // Focus on name field
            NameTextBox.Focus();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!await ValidateFormAsync())
                return;

            try
            {
                SaveButton.IsEnabled = false;
                
                var currency = new Currency
                {
                    Name = NameTextBox.Text.Trim(),
                    Code = CodeTextBox.Text.Trim().ToUpper(),
                    Symbol = SymbolTextBox.Text.Trim(),
                    IsGold = IsGoldCheckBox.IsChecked == true
                };

                bool success;
                if (_isEditMode && _existingCurrency != null)
                {
                    currency.Id = _existingCurrency.Id;
                    currency.CreatedDate = _existingCurrency.CreatedDate;
                    currency.IsActive = _existingCurrency.IsActive;
                    success = await _currencyService.UpdateCurrencyAsync(currency);
                }
                else
                {
                    var newId = await _currencyService.AddCurrencyAsync(currency);
                    success = newId > 0;
                }

                if (success)
                {
                    var message = _isEditMode ? "تم تحديث بيانات العملة بنجاح" : "تم إضافة العملة بنجاح";
                    MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    var message = _isEditMode ? "فشل في تحديث بيانات العملة" : "فشل في إضافة العملة";
                    MessageBox.Show(message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                var action = _isEditMode ? "تحديث" : "إضافة";
                MessageBox.Show($"خطأ في {action} العملة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }

        private async Task<bool> ValidateFormAsync()
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رمز العملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CodeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(SymbolTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الرمز المختصر للعملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SymbolTextBox.Focus();
                return false;
            }

            // Validate name length
            if (NameTextBox.Text.Trim().Length < 2)
            {
                MessageBox.Show("يجب أن يكون اسم العملة أكثر من حرف واحد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            // Validate code length
            if (CodeTextBox.Text.Trim().Length < 2)
            {
                MessageBox.Show("يجب أن يكون رمز العملة أكثر من حرف واحد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CodeTextBox.Focus();
                return false;
            }

            // Check if currency code already exists
            try
            {
                var excludeId = _isEditMode ? _existingCurrency?.Id : null;
                var codeExists = await _currencyService.IsCurrencyCodeExistsAsync(CodeTextBox.Text.Trim().ToUpper(), excludeId);
                if (codeExists)
                {
                    MessageBox.Show("رمز العملة موجود مسبقاً، يرجى اختيار رمز آخر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    CodeTextBox.Focus();
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من رمز العملة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
