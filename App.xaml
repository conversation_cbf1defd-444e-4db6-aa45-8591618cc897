<Application x:Class="DebtManager.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Teal" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Colors -->
                    <SolidColorBrush x:Key="ProfitColor" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="DebtColor" Color="#F44336"/>
                    <SolidColorBrush x:Key="NeutralColor" Color="#9E9E9E"/>
                    <SolidColorBrush x:Key="BackgroundColor" Color="#FAFAFA"/>
                    
                    <!-- Card Style -->
                    <Style x:Key="CardStyle" TargetType="Border">
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="CornerRadius" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                            </Setter.Value>
                        </Setter>
                    </Style>
                    
                    <!-- Button Styles -->
                    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>
                    
                    <!-- DataGrid Style -->
                    <Style x:Key="ModernDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                        <Setter Property="AutoGenerateColumns" Value="False"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="IsReadOnly" Value="True"/>
                        <Setter Property="SelectionMode" Value="Single"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="AlternatingRowBackground" Value="#F5F5F5"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
