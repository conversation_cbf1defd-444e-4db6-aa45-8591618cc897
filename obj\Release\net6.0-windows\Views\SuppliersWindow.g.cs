﻿#pragma checksum "..\..\..\..\Views\SuppliersWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "67E38BDA6FEEC9F9CCA75000303DDE849778E51C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Views {
    
    
    /// <summary>
    /// SuppliersWindow
    /// </summary>
    public partial class SuppliersWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\..\Views\SuppliersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Views\SuppliersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddSupplierButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\SuppliersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditSupplierButton;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\SuppliersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewDebtsButton;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\SuppliersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteSupplierButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\SuppliersWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SuppliersDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/views/supplierswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SuppliersWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 45 "..\..\..\..\Views\SuppliersWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BackButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 68 "..\..\..\..\Views\SuppliersWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AddSupplierButton = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\Views\SuppliersWindow.xaml"
            this.AddSupplierButton.Click += new System.Windows.RoutedEventHandler(this.AddSupplierButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.EditSupplierButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\SuppliersWindow.xaml"
            this.EditSupplierButton.Click += new System.Windows.RoutedEventHandler(this.EditSupplierButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ViewDebtsButton = ((System.Windows.Controls.Button)(target));
            
            #line 93 "..\..\..\..\Views\SuppliersWindow.xaml"
            this.ViewDebtsButton.Click += new System.Windows.RoutedEventHandler(this.ViewDebtsButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DeleteSupplierButton = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\Views\SuppliersWindow.xaml"
            this.DeleteSupplierButton.Click += new System.Windows.RoutedEventHandler(this.DeleteSupplierButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SuppliersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 132 "..\..\..\..\Views\SuppliersWindow.xaml"
            this.SuppliersDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SuppliersDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 133 "..\..\..\..\Views\SuppliersWindow.xaml"
            this.SuppliersDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.SuppliersDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

