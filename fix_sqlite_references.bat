@echo off
echo Fixing SQLite references in all files...

powershell -Command "(Get-Content 'Services\ClientService.cs') -replace 'SQLiteConnection', 'SqliteConnection' -replace 'SQLiteCommand', 'SqliteCommand' | Set-Content 'Services\ClientService.cs'"
powershell -Command "(Get-Content 'Services\SupplierService.cs') -replace 'using System.Data.SQLite;', 'using Microsoft.Data.Sqlite;' -replace 'SQLiteConnection', 'SqliteConnection' -replace 'SQLiteCommand', 'SqliteCommand' | Set-Content 'Services\SupplierService.cs'"
powershell -Command "(Get-Content 'Services\CurrencyService.cs') -replace 'using System.Data.SQLite;', 'using Microsoft.Data.Sqlite;' -replace 'SQLiteConnection', 'SqliteConnection' -replace 'SQLiteCommand', 'SqliteCommand' | Set-Content 'Services\CurrencyService.cs'"
powershell -Command "(Get-Content 'Services\DebtService.cs') -replace 'using System.Data.SQLite;', 'using Microsoft.Data.Sqlite;' -replace 'SQLiteConnection', 'SqliteConnection' -replace 'SQLiteCommand', 'SqliteCommand' | Set-Content 'Services\DebtService.cs'"

echo SQLite references fixed!
pause
