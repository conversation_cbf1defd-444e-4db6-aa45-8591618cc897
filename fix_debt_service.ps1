# Fix DebtService.cs reader methods
Write-Host "Fixing DebtService.cs reader methods..."

$file = "Services\DebtService.cs"

if (Test-Path $file) {
    $content = Get-Content $file -Raw
    
    # Fix all remaining reader.GetXXX methods
    $content = $content -replace 'reader\.GetInt32\("([^"]+)"\)', 'reader.GetInt32(reader.GetOrdinal("$1"))'
    $content = $content -replace 'reader\.GetString\("([^"]+)"\)', 'reader.GetString(reader.GetOrdinal("$1"))'
    $content = $content -replace 'reader\.GetBoolean\("([^"]+)"\)', 'reader.GetBoolean(reader.GetOrdinal("$1"))'
    $content = $content -replace 'reader\.GetDateTime\("([^"]+)"\)', 'reader.GetDateTime(reader.GetOrdinal("$1"))'
    $content = $content -replace 'reader\.GetDecimal\("([^"]+)"\)', 'reader.GetDecimal(reader.GetOrdinal("$1"))'
    
    # Fix IsDBNull calls that don't already use GetOrdinal
    $content = $content -replace 'reader\.IsDBNull\("([^"]+)"\)', 'reader.IsDBNull(reader.GetOrdinal("$1"))'
    
    # Fix double GetOrdinal calls (in case they already exist)
    $content = $content -replace 'reader\.GetOrdinal\(reader\.GetOrdinal\("([^"]+)"\)\)', 'reader.GetOrdinal("$1")'
    
    Set-Content $file $content -NoNewline
    Write-Host "Fixed $file"
} else {
    Write-Host "File not found: $file"
}

Write-Host "DebtService.cs fixed!"
