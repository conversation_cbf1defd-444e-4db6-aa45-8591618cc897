using Microsoft.Data.Sqlite;
using System;
using System.IO;

namespace DebtManager.Data
{
    public static class DatabaseService
    {
        private static readonly string DatabasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DebtManager.db");
        public static string ConnectionString => $"Data Source={DatabasePath}";

        public static void InitializeDatabase()
        {
            using var connection = new SqliteConnection(ConnectionString);
            connection.Open();

            // Create tables
            CreateTables(connection);
            
            // Insert default data
            InsertDefaultData(connection);
        }

        private static void CreateTables(SqliteConnection connection)
        {
            var commands = new[]
            {
                // Currencies table
                @"CREATE TABLE IF NOT EXISTS Currencies (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name NVARCHAR(50) NOT NULL,
                    Code NVARCHAR(10) NOT NULL UNIQUE,
                    Symbol NVARCHAR(10) NOT NULL,
                    IsGold BOOLEAN NOT NULL DEFAULT 0,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                )",

                // Clients table
                @"CREATE TABLE IF NOT EXISTS Clients (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name NVARCHAR(100) NOT NULL,
                    Phone NVARCHAR(20),
                    Address NVARCHAR(200),
                    Notes NVARCHAR(500),
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                )",

                // Suppliers table
                @"CREATE TABLE IF NOT EXISTS Suppliers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name NVARCHAR(100) NOT NULL,
                    Phone NVARCHAR(20),
                    Address NVARCHAR(200),
                    Company NVARCHAR(100),
                    Notes NVARCHAR(500),
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
                )",

                // Debts table
                @"CREATE TABLE IF NOT EXISTS Debts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ClientId INTEGER,
                    SupplierId INTEGER,
                    CurrencyId INTEGER NOT NULL,
                    Amount DECIMAL(18,4) NOT NULL,
                    DebtType INTEGER NOT NULL,
                    TransactionType INTEGER NOT NULL,
                    TransactionDate DATETIME NOT NULL,
                    DueDate DATETIME,
                    Notes NVARCHAR(500),
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    IsPaid BOOLEAN NOT NULL DEFAULT 0,
                    PaidDate DATETIME,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy NVARCHAR(50),
                    FOREIGN KEY (ClientId) REFERENCES Clients(Id),
                    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id),
                    FOREIGN KEY (CurrencyId) REFERENCES Currencies(Id)
                )"
            };

            foreach (var command in commands)
            {
                using var cmd = new SqliteCommand(command, connection);
                cmd.ExecuteNonQuery();
            }
        }

        private static void InsertDefaultData(SqliteConnection connection)
        {
            // Check if currencies already exist
            using var checkCmd = new SqliteCommand("SELECT COUNT(*) FROM Currencies", connection);
            var count = Convert.ToInt32(checkCmd.ExecuteScalar());
            
            if (count == 0)
            {
                var currencies = new[]
                {
                    ("الليرة السورية", "SYP", "ل.س", false),
                    ("الدولار الأمريكي", "USD", "$", false),
                    ("اليورو", "EUR", "€", false),
                    ("الذهب", "GOLD", "غ", true),
                    ("الليرة التركية", "TRY", "₺", false)
                };

                foreach (var (name, code, symbol, isGold) in currencies)
                {
                    using var cmd = new SqliteCommand(
                        "INSERT INTO Currencies (Name, Code, Symbol, IsGold) VALUES (@name, @code, @symbol, @isGold)",
                        connection);
                    cmd.Parameters.AddWithValue("@name", name);
                    cmd.Parameters.AddWithValue("@code", code);
                    cmd.Parameters.AddWithValue("@symbol", symbol);
                    cmd.Parameters.AddWithValue("@isGold", isGold);
                    cmd.ExecuteNonQuery();
                }
            }
        }
    }
}
