# Fix all compilation issues
Write-Host "Fixing all compilation issues..."

# Fix DatabaseService.cs - Add missing using statements
$content = Get-Content "Data\DatabaseService.cs" -Raw
$content = $content -replace "using Microsoft\.Data\.Sqlite;", "using Microsoft.Data.Sqlite;`nusing System;`nusing System.IO;"
Set-Content "Data\DatabaseService.cs" $content -NoNewline

# Fix all service files - Add missing using statements
$serviceFiles = @(
    "Services\ClientService.cs",
    "Services\SupplierService.cs", 
    "Services\CurrencyService.cs",
    "Services\DebtService.cs"
)

foreach ($file in $serviceFiles) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        
        # Add System namespace
        if ($content -notmatch "using System;") {
            $content = $content -replace "(using Microsoft\.Data\.Sqlite;)", "`$1`nusing System;"
        }
        
        # Fix reader.GetXXX methods - use ordinal indexing
        $content = $content -replace 'reader\.GetInt32\("([^"]+)"\)', 'reader.GetInt32("$1")'
        $content = $content -replace 'reader\.GetString\("([^"]+)"\)', 'reader.GetString("$1")'
        $content = $content -replace 'reader\.GetBoolean\("([^"]+)"\)', 'reader.GetBoolean("$1")'
        $content = $content -replace 'reader\.GetDateTime\("([^"]+)"\)', 'reader.GetDateTime("$1")'
        $content = $content -replace 'reader\.GetDecimal\("([^"]+)"\)', 'reader.GetDecimal("$1")'
        $content = $content -replace 'reader\.IsDBNull\("([^"]+)"\)', 'reader.IsDBNull(reader.GetOrdinal("$1"))'
        
        Set-Content $file $content -NoNewline
        Write-Host "Fixed $file"
    }
}

# Fix Models - Add LINQ using
$modelFiles = @(
    "Models\Client.cs",
    "Models\Supplier.cs"
)

foreach ($file in $modelFiles) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        
        # Add LINQ using
        if ($content -notmatch "using System\.Linq;") {
            $content = $content -replace "(using System\.Collections\.Generic;)", "`$1`nusing System.Linq;"
        }
        
        Set-Content $file $content -NoNewline
        Write-Host "Fixed $file"
    }
}

# Fix View files - Add missing using statements
$viewFiles = @(
    "Views\MainWindow.xaml.cs",
    "Views\ClientsWindow.xaml.cs",
    "Views\SuppliersWindow.xaml.cs",
    "Views\CurrenciesWindow.xaml.cs",
    "Views\DebtWindow.xaml.cs",
    "Views\ClientDialog.xaml.cs",
    "Views\SupplierDialog.xaml.cs",
    "Views\CurrencyDialog.xaml.cs",
    "Views\ClientDebtsWindow.xaml.cs",
    "Views\SupplierDebtsWindow.xaml.cs"
)

foreach ($file in $viewFiles) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        
        # Add missing using statements
        if ($content -notmatch "using System;") {
            $content = $content -replace "(using System\.Collections\.Generic;)", "using System;`n`$1"
        }
        if ($content -notmatch "using System\.Linq;") {
            $content = $content -replace "(using System\.Threading\.Tasks;)", "`$1`nusing System.Linq;"
        }
        
        Set-Content $file $content -NoNewline
        Write-Host "Fixed $file"
    }
}

Write-Host "All issues fixed!"
