using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Services;

namespace DebtManager.Views
{
    public partial class NotificationsWindow : Window
    {
        private readonly NotificationService _notificationService;
        private List<NotificationItem> _allNotifications = new();
        private List<NotificationItem> _filteredNotifications = new();

        public NotificationsWindow()
        {
            InitializeComponent();
            _notificationService = new NotificationService();
        }

        private async void NotificationsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadNotificationsAsync();
        }

        private async Task LoadNotificationsAsync()
        {
            try
            {
                _allNotifications = await _notificationService.GetPendingNotificationsAsync();
                ApplyFilters();
                UpdateSummaryCards();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التنبيهات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            _filteredNotifications = _allNotifications.ToList();

            // Priority filter
            var selectedPriority = ((ComboBoxItem)PriorityFilterComboBox.SelectedItem).Tag.ToString();
            if (selectedPriority != "All")
            {
                if (Enum.TryParse<NotificationPriority>(selectedPriority, out NotificationPriority priority))
                {
                    _filteredNotifications = _filteredNotifications.Where(n => n.Priority == priority).ToList();
                }
            }

            // Type filter
            var selectedType = ((ComboBoxItem)TypeFilterComboBox.SelectedItem).Tag.ToString();
            if (selectedType != "All")
            {
                if (Enum.TryParse<NotificationType>(selectedType, out NotificationType type))
                {
                    _filteredNotifications = _filteredNotifications.Where(n => n.Type == type).ToList();
                }
            }

            // Update UI
            NotificationsItemsControl.ItemsSource = _filteredNotifications;
        }

        private void UpdateSummaryCards()
        {
            var highPriorityCount = _allNotifications.Count(n => n.Priority == NotificationPriority.High);
            var mediumPriorityCount = _allNotifications.Count(n => n.Priority == NotificationPriority.Medium);
            var totalCount = _allNotifications.Count;

            HighPriorityCountText.Text = highPriorityCount.ToString();
            MediumPriorityCountText.Text = mediumPriorityCount.ToString();
            TotalNotificationsText.Text = totalCount.ToString();
        }

        private void PriorityFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications.Any())
            {
                ApplyFilters();
            }
        }

        private void TypeFilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_allNotifications.Any())
            {
                ApplyFilters();
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadNotificationsAsync();
        }

        private void MarkAllReadButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تجاهل جميع التنبيهات؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                _allNotifications.Clear();
                _filteredNotifications.Clear();
                NotificationsItemsControl.ItemsSource = _filteredNotifications;
                UpdateSummaryCards();
            }
        }

        private void ViewDetailsButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is NotificationItem notification)
            {
                var details = $"تفاصيل التنبيه:\n\n";
                details += $"العنوان: {notification.Title}\n";
                details += $"الرسالة: {notification.Message}\n";
                details += $"الأولوية: {GetPriorityText(notification.Priority)}\n";
                details += $"النوع: {GetTypeText(notification.Type)}\n";
                details += $"التاريخ: {notification.CreatedDate:dd/MM/yyyy HH:mm}\n";

                if (!string.IsNullOrEmpty(notification.PersonName))
                {
                    details += $"الشخص: {notification.PersonName}\n";
                }

                if (notification.Amount.HasValue)
                {
                    details += $"المبلغ: {notification.Amount:N2} {notification.Currency}\n";
                }

                if (notification.DueDate.HasValue)
                {
                    details += $"تاريخ الاستحقاق: {notification.DueDate:dd/MM/yyyy}\n";
                }

                MessageBox.Show(details, "تفاصيل التنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DismissButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is NotificationItem notification)
            {
                _allNotifications.Remove(notification);
                ApplyFilters();
                UpdateSummaryCards();
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var settings = "إعدادات التنبيهات:\n\n";
            settings += "• تنبيهات الديون المتأخرة: مفعلة\n";
            settings += "• تنبيهات الديون المستحقة قريباً: مفعلة\n";
            settings += "• تنبيهات المبالغ الكبيرة: مفعلة (أكثر من 10,000)\n";
            settings += "• تنبيهات الملخص العام: مفعلة (5 ديون متأخرة أو أكثر)\n\n";
            settings += "يمكن تخصيص هذه الإعدادات في التحديثات القادمة.";

            MessageBox.Show(settings, "إعدادات التنبيهات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private string GetPriorityText(NotificationPriority priority)
        {
            return priority switch
            {
                NotificationPriority.High => "عالية",
                NotificationPriority.Medium => "متوسطة",
                NotificationPriority.Low => "منخفضة",
                _ => "غير محدد"
            };
        }

        private string GetTypeText(NotificationType type)
        {
            return type switch
            {
                NotificationType.Overdue => "دين متأخر",
                NotificationType.DueSoon => "مستحق قريباً",
                NotificationType.LargeAmount => "مبلغ كبير",
                NotificationType.Summary => "ملخص عام",
                NotificationType.Custom => "مخصص",
                _ => "غير محدد"
            };
        }
    }
}
