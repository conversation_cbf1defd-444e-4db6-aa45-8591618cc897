<Window x:Class="DebtManager.Views.CurrencyDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة/تعديل عملة" 
        Height="450" Width="500"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                   Width="32" Height="32" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   VerticalAlignment="Center"/>
            <TextBlock x:Name="HeaderText"
                     Text="إضافة عملة جديدة" 
                     FontSize="24" FontWeight="Bold" 
                     Foreground="{DynamicResource PrimaryHueMidBrush}" 
                     VerticalAlignment="Center" 
                     Margin="10,0,0,0"/>
        </StackPanel>
        
        <!-- Form -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <StackPanel>
                <!-- Currency Name -->
                <TextBox x:Name="NameTextBox"
                         materialDesign:HintAssist.Hint="اسم العملة *"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="50"
                         Margin="0,0,0,15"/>

                <!-- Currency Code -->
                <TextBox x:Name="CodeTextBox"
                         materialDesign:HintAssist.Hint="رمز العملة (مثل USD, EUR) *"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="10"
                         CharacterCasing="Upper"
                         Margin="0,0,0,15"/>

                <!-- Currency Symbol -->
                <TextBox x:Name="SymbolTextBox"
                         materialDesign:HintAssist.Hint="الرمز المختصر (مثل $, €) *"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="10"
                         Margin="0,0,0,15"/>

                <!-- Is Gold Checkbox -->
                <CheckBox x:Name="IsGoldCheckBox"
                          Content="هذه العملة تمثل الذهب"
                          FontSize="14"
                          Margin="0,0,0,15"/>

                <!-- Info Panel -->
                <Border Background="#E3F2FD"
                        CornerRadius="4"
                        Padding="10"
                        Margin="0,0,0,15">
                    <TextBlock Text="ملاحظة: العملات التي تمثل الذهب يتم التعامل معها بوحدة الغرام"
                             TextWrapping="Wrap"
                             FontSize="12"
                             Foreground="#1976D2"/>
                </Border>

                <!-- Required Field Note -->
                <TextBlock Text="* الحقول المطلوبة"
                         FontSize="12"
                         Foreground="Gray"
                         HorizontalAlignment="Right"/>
            </StackPanel>
        </Border>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            <Button x:Name="SaveButton" 
                    Style="{StaticResource PrimaryButton}"
                    Click="SaveButton_Click"
                    Width="120">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20"/>
                    <TextBlock Text="حفظ" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="CancelButton" 
                    Style="{StaticResource SecondaryButton}"
                    Click="CancelButton_Click"
                    Width="120">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cancel" Width="20" Height="20"/>
                    <TextBlock Text="إلغاء" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
