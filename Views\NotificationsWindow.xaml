<Window x:Class="DebtManager.Views.NotificationsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="التنبيهات والإشعارات" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        ResizeMode="CanResize"
        MinHeight="500" MinWidth="600"
        Loaded="NotificationsWindow_Loaded">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="Bell" 
                                   Width="32" Height="32" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   VerticalAlignment="Center"/>
            <TextBlock Text="التنبيهات والإشعارات" 
                     FontSize="24" FontWeight="Bold" 
                     Foreground="{DynamicResource PrimaryHueMidBrush}" 
                     VerticalAlignment="Center" 
                     Margin="10,0,0,0"/>
        </StackPanel>
        
        <!-- Summary Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- High Priority -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Background="#FFE8E8">
                <StackPanel HorizontalAlignment="Center" Margin="15">
                    <materialDesign:PackIcon Kind="AlertCircle" Width="24" Height="24" 
                                           Foreground="Red" HorizontalAlignment="Center"/>
                    <TextBlock Text="عالية الأولوية" FontWeight="Bold" 
                             HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="HighPriorityCountText" Text="0" FontSize="16" 
                             FontWeight="Bold" Foreground="Red" 
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Medium Priority -->
            <Border Grid.Column="2" Style="{StaticResource CardStyle}" Background="#FFF0E8">
                <StackPanel HorizontalAlignment="Center" Margin="15">
                    <materialDesign:PackIcon Kind="Alert" Width="24" Height="24" 
                                           Foreground="Orange" HorizontalAlignment="Center"/>
                    <TextBlock Text="متوسطة الأولوية" FontWeight="Bold" 
                             HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="MediumPriorityCountText" Text="0" FontSize="16" 
                             FontWeight="Bold" Foreground="Orange" 
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Total -->
            <Border Grid.Column="4" Style="{StaticResource CardStyle}" Background="#E8F0FF">
                <StackPanel HorizontalAlignment="Center" Margin="15">
                    <materialDesign:PackIcon Kind="BellRing" Width="24" Height="24" 
                                           Foreground="Blue" HorizontalAlignment="Center"/>
                    <TextBlock Text="إجمالي التنبيهات" FontWeight="Bold" 
                             HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TotalNotificationsText" Text="0" FontSize="16" 
                             FontWeight="Bold" Foreground="Blue" 
                             HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- Notifications List -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Filter Options -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="فلترة:" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox x:Name="PriorityFilterComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            Width="150" Margin="0,0,10,0"
                            SelectionChanged="PriorityFilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الأولويات" Tag="All" IsSelected="True"/>
                        <ComboBoxItem Content="عالية الأولوية" Tag="High"/>
                        <ComboBoxItem Content="متوسطة الأولوية" Tag="Medium"/>
                        <ComboBoxItem Content="منخفضة الأولوية" Tag="Low"/>
                    </ComboBox>
                    
                    <ComboBox x:Name="TypeFilterComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            Width="150"
                            SelectionChanged="TypeFilterComboBox_SelectionChanged">
                        <ComboBoxItem Content="جميع الأنواع" Tag="All" IsSelected="True"/>
                        <ComboBoxItem Content="ديون متأخرة" Tag="Overdue"/>
                        <ComboBoxItem Content="مستحقة قريباً" Tag="DueSoon"/>
                        <ComboBoxItem Content="مبالغ كبيرة" Tag="LargeAmount"/>
                        <ComboBoxItem Content="ملخص عام" Tag="Summary"/>
                    </ComboBox>
                </StackPanel>
                
                <!-- Notifications List -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl x:Name="NotificationsItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource CardStyle}" Margin="0,0,0,10">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- Priority Icon -->
                                        <materialDesign:PackIcon Grid.Column="0" 
                                                               Width="24" Height="24" 
                                                               VerticalAlignment="Top" 
                                                               Margin="0,0,15,0">
                                            <materialDesign:PackIcon.Style>
                                                <Style TargetType="materialDesign:PackIcon">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Priority}" Value="High">
                                                            <Setter Property="Kind" Value="AlertCircle"/>
                                                            <Setter Property="Foreground" Value="Red"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Priority}" Value="Medium">
                                                            <Setter Property="Kind" Value="Alert"/>
                                                            <Setter Property="Foreground" Value="Orange"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Priority}" Value="Low">
                                                            <Setter Property="Kind" Value="Information"/>
                                                            <Setter Property="Foreground" Value="Blue"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </materialDesign:PackIcon.Style>
                                        </materialDesign:PackIcon>
                                        
                                        <!-- Content -->
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="{Binding Title}" FontWeight="Bold" FontSize="14"/>
                                            <TextBlock Text="{Binding Message}" TextWrapping="Wrap" Margin="0,5,0,0"/>
                                            <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                                <TextBlock Text="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}" 
                                                         FontSize="12" Foreground="Gray"/>
                                                <TextBlock Text=" • " FontSize="12" Foreground="Gray" Margin="5,0"/>
                                                <TextBlock Text="{Binding PersonName}" FontSize="12" Foreground="Gray"/>
                                            </StackPanel>
                                        </StackPanel>
                                        
                                        <!-- Actions -->
                                        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Top">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  ToolTip="عرض التفاصيل"
                                                  Click="ViewDetailsButton_Click"
                                                  Tag="{Binding}">
                                                <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                  ToolTip="تجاهل"
                                                  Click="DismissButton_Click"
                                                  Tag="{Binding}">
                                                <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Border>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="RefreshButton" 
                    Style="{StaticResource PrimaryButton}"
                    Click="RefreshButton_Click"
                    Width="120" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                    <TextBlock Text="تحديث" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="MarkAllReadButton" 
                    Style="{StaticResource SecondaryButton}"
                    Click="MarkAllReadButton_Click"
                    Width="120" Margin="0,0,10,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CheckAll" Width="20" Height="20"/>
                    <TextBlock Text="تجاهل الكل" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="SettingsButton"
                    Style="{StaticResource SecondaryButton}"
                    Click="SettingsButton_Click"
                    Width="120">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Settings" Width="20" Height="20"/>
                    <TextBlock Text="الإعدادات" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
