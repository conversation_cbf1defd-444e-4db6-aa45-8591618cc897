﻿#pragma checksum "..\..\..\..\Views\NotificationsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "663C20A85B9B27E3BEAFB25B9DF4896E97953972"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Views {
    
    
    /// <summary>
    /// NotificationsWindow
    /// </summary>
    public partial class NotificationsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 54 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HighPriorityCountText;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MediumPriorityCountText;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalNotificationsText;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PriorityFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl NotificationsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MarkAllReadButton;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\NotificationsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/views/notificationswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\NotificationsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 14 "..\..\..\..\Views\NotificationsWindow.xaml"
            ((DebtManager.Views.NotificationsWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.NotificationsWindow_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.HighPriorityCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.MediumPriorityCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TotalNotificationsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PriorityFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 101 "..\..\..\..\Views\NotificationsWindow.xaml"
            this.PriorityFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PriorityFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 111 "..\..\..\..\Views\NotificationsWindow.xaml"
            this.TypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TypeFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.NotificationsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 10:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\Views\NotificationsWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.MarkAllReadButton = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\..\Views\NotificationsWindow.xaml"
            this.MarkAllReadButton.Click += new System.Windows.RoutedEventHandler(this.MarkAllReadButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\Views\NotificationsWindow.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 8:
            
            #line 174 "..\..\..\..\Views\NotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDetailsButton_Click);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 180 "..\..\..\..\Views\NotificationsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DismissButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

