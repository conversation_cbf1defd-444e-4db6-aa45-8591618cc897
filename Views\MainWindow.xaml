<Window x:Class="DebtManager.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="DebtManager - إدارة ديون العملاء والموردين" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="AccountMultiple" 
                                           Width="32" Height="32" 
                                           Foreground="White" 
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="إدارة ديون العملاء والموردين" 
                             FontSize="24" FontWeight="Bold" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             Margin="10,0,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="CurrentDateText" 
                             FontSize="16" 
                             Foreground="White" 
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Navigation Buttons -->
                <Border Grid.Row="0" Style="{StaticResource CardStyle}">
                    <UniformGrid Rows="1" Columns="4">
                        <Button x:Name="ClientsButton" 
                                Style="{StaticResource PrimaryButton}"
                                Click="ClientsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Account" Width="20" Height="20"/>
                                <TextBlock Text="العملاء" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="SuppliersButton" 
                                Style="{StaticResource PrimaryButton}"
                                Click="SuppliersButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="TruckDelivery" Width="20" Height="20"/>
                                <TextBlock Text="الموردين" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="AddDebtButton"
                                Style="{StaticResource PrimaryButton}"
                                Click="AddDebtButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="20" Height="20"/>
                                <TextBlock Text="إضافة دين" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ReportsButton"
                                Style="{StaticResource SecondaryButton}"
                                Click="ReportsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20"/>
                                <TextBlock Text="التقارير" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SearchButton"
                                Style="{StaticResource SecondaryButton}"
                                Click="SearchButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20"/>
                                <TextBlock Text="البحث المتقدم" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="NotificationsButton"
                                Style="{StaticResource SecondaryButton}"
                                Click="NotificationsButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Bell" Width="20" Height="20"/>
                                <TextBlock Text="التنبيهات" Margin="8,0,0,0"/>
                                <Border x:Name="NotificationBadge"
                                      Background="Red"
                                      CornerRadius="10"
                                      MinWidth="20" Height="20"
                                      Margin="5,0,0,0"
                                      Visibility="Collapsed">
                                    <TextBlock x:Name="NotificationCountText"
                                             Text="0"
                                             Foreground="White"
                                             FontSize="10"
                                             FontWeight="Bold"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center"/>
                                </Border>
                            </StackPanel>
                        </Button>

                        <Button x:Name="CurrenciesButton"
                                Style="{StaticResource SecondaryButton}"
                                Click="CurrenciesButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20"/>
                                <TextBlock Text="العملات" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </UniformGrid>
                </Border>
                
                <!-- Summary Cards -->
                <UniformGrid Grid.Row="1" Rows="1" Columns="4" Margin="0,10">
                    <!-- Total Debts For Us -->
                    <Border Style="{StaticResource CardStyle}" Background="#E8F5E8">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingUp" 
                                                       Width="24" Height="24" 
                                                       Foreground="{StaticResource ProfitColor}"/>
                                <TextBlock Text="ديون لنا" 
                                         FontSize="16" FontWeight="Bold" 
                                         Foreground="{StaticResource ProfitColor}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TotalDebtsForUsText" 
                                     Text="0" 
                                     FontSize="20" FontWeight="Bold" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Total Debts On Us -->
                    <Border Style="{StaticResource CardStyle}" Background="#FFEBEE">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingDown" 
                                                       Width="24" Height="24" 
                                                       Foreground="{StaticResource DebtColor}"/>
                                <TextBlock Text="ديون علينا" 
                                         FontSize="16" FontWeight="Bold" 
                                         Foreground="{StaticResource DebtColor}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TotalDebtsOnUsText" 
                                     Text="0" 
                                     FontSize="20" FontWeight="Bold" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Overdue Debts -->
                    <Border Style="{StaticResource CardStyle}" Background="#FFF3E0">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="AlertCircle" 
                                                       Width="24" Height="24" 
                                                       Foreground="#FF9800"/>
                                <TextBlock Text="ديون متأخرة" 
                                         FontSize="16" FontWeight="Bold" 
                                         Foreground="#FF9800"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="OverdueDebtsText" 
                                     Text="0" 
                                     FontSize="20" FontWeight="Bold" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Total Clients & Suppliers -->
                    <Border Style="{StaticResource CardStyle}" Background="#F3E5F5">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="AccountGroup" 
                                                       Width="24" Height="24" 
                                                       Foreground="#9C27B0"/>
                                <TextBlock Text="إجمالي الأشخاص" 
                                         FontSize="16" FontWeight="Bold" 
                                         Foreground="#9C27B0"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TotalPeopleText" 
                                     Text="0" 
                                     FontSize="20" FontWeight="Bold" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,10,0,0"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>
                
                <!-- Alerts Section -->
                <Border x:Name="AlertsSection" Grid.Row="2" Style="{StaticResource CardStyle}" 
                        Background="#FFEBEE" Visibility="Collapsed" Margin="0,10">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Bell" 
                                                   Width="24" Height="24" 
                                                   Foreground="{StaticResource DebtColor}"/>
                            <TextBlock Text="تنبيهات مهمة" 
                                     FontSize="18" FontWeight="Bold" 
                                     Foreground="{StaticResource DebtColor}"
                                     Margin="8,0,0,0"/>
                        </StackPanel>
                        <ItemsControl x:Name="AlertsList" Margin="0,10,0,0">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="White" 
                                            CornerRadius="4" 
                                            Padding="10" 
                                            Margin="0,2">
                                        <TextBlock Text="{Binding}" 
                                                 TextWrapping="Wrap"/>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>
                
                <!-- Recent Transactions -->
                <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="0,10,0,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" 
                                 Text="آخر المعاملات" 
                                 FontSize="18" FontWeight="Bold" 
                                 HorizontalAlignment="Center" 
                                 Margin="0,0,0,10"/>
                        
                        <DataGrid x:Name="RecentTransactionsGrid" 
                                Grid.Row="1"
                                Style="{StaticResource ModernDataGrid}"
                                MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="التاريخ والوقت"
                                                  Binding="{Binding TransactionDate, StringFormat=dd/MM/yyyy HH:mm}"
                                                  Width="140"/>
                                <DataGridTextColumn Header="الشخص" 
                                                  Binding="{Binding PersonName}" 
                                                  Width="150"/>
                                <DataGridTextColumn Header="المبلغ" 
                                                  Binding="{Binding Amount, StringFormat=N2}" 
                                                  Width="100"/>
                                <DataGridTextColumn Header="العملة" 
                                                  Binding="{Binding Currency.Symbol}" 
                                                  Width="80"/>
                                <DataGridTextColumn Header="النوع" 
                                                  Binding="{Binding DebtTypeText}" 
                                                  Width="120"/>
                                <DataGridTextColumn Header="الحالة" 
                                                  Binding="{Binding StatusText}" 
                                                  Width="100"/>
                                <DataGridTextColumn Header="الملاحظات" 
                                                  Binding="{Binding Notes}" 
                                                  Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
            </Grid>
        </ScrollViewer>
    </Grid>
</Window>
