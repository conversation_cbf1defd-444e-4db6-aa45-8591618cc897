using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class ClientDebtsWindow : Window
    {
        private readonly Client _client;
        private readonly DebtService _debtService;
        private List<Debt> _clientDebts = new();

        public ClientDebtsWindow(Client client)
        {
            InitializeComponent();
            _client = client;
            _debtService = new DebtService();
            
            InitializeWindow();
            Loaded += ClientDebtsWindow_Loaded;
        }

        private void InitializeWindow()
        {
            HeaderText.Text = $"ديون العميل: {_client.Name}";
            Title = $"ديون العميل: {_client.Name}";
            ClientNameText.Text = _client.Name;
            ClientPhoneText.Text = _client.Phone ?? "غير محدد";
        }

        private async void ClientDebtsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadClientDebtsAsync();
        }

        private async Task LoadClientDebtsAsync()
        {
            try
            {
                _clientDebts = await _debtService.GetDebtsByClientIdAsync(_client.Id);
                DebtsDataGrid.ItemsSource = _clientDebts;
                
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل ديون العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSummary()
        {
            var activeDebts = _clientDebts.Where(d => d.IsActive && !d.IsPaid).ToList();

            // Calculate totals by currency
            var currencySummary = new Dictionary<string, CurrencyBalance>();

            foreach (var debt in activeDebts)
            {
                var currencyKey = $"{debt.Currency.Name} ({debt.Currency.Symbol})";

                if (!currencySummary.ContainsKey(currencyKey))
                {
                    currencySummary[currencyKey] = new CurrencyBalance
                    {
                        CurrencyName = debt.Currency.Name,
                        CurrencySymbol = debt.Currency.Symbol,
                        ForUs = 0,
                        OnUs = 0
                    };
                }

                if (debt.DebtType == DebtType.ClientOwesUs)
                {
                    currencySummary[currencyKey].ForUs += debt.Amount;
                }
                else if (debt.DebtType == DebtType.WeOweClient)
                {
                    currencySummary[currencyKey].OnUs += debt.Amount;
                }
            }

            // Build summary text
            var debtText = "";
            var creditText = "";
            var netText = "";
            var hasPositiveBalance = false;
            var hasNegativeBalance = false;

            foreach (var currency in currencySummary.Values)
            {
                var netBalance = currency.ForUs - currency.OnUs;

                if (currency.ForUs > 0)
                    debtText += $"{currency.ForUs:N0} {currency.CurrencySymbol}\n";

                if (currency.OnUs > 0)
                    creditText += $"{currency.OnUs:N0} {currency.CurrencySymbol}\n";

                if (netBalance > 0)
                {
                    netText += $"{netBalance:N0} {currency.CurrencySymbol}\n";
                    hasPositiveBalance = true;
                }
                else if (netBalance < 0)
                {
                    netText += $"- {Math.Abs(netBalance):N0} {currency.CurrencySymbol}\n";
                    hasNegativeBalance = true;
                }
            }

            TotalDebtText.Text = string.IsNullOrEmpty(debtText) ? "0" : debtText.Trim();
            TotalCreditText.Text = string.IsNullOrEmpty(creditText) ? "0" : creditText.Trim();
            NetBalanceText.Text = string.IsNullOrEmpty(netText) ? "0" : netText.Trim();

            // Apply colors to all text elements
            TotalDebtText.Foreground = (System.Windows.Media.Brush)FindResource("ProfitColor");
            TotalCreditText.Foreground = (System.Windows.Media.Brush)FindResource("DebtColor");

            // Color coding for net balance
            if (hasPositiveBalance && !hasNegativeBalance)
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("ProfitColor");
            }
            else if (hasNegativeBalance && !hasPositiveBalance)
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("DebtColor");
            }
            else
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("NeutralColor");
            }
        }

        private void DebtsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedDebt = DebtsDataGrid.SelectedItem as Debt;
            MarkPaidButton.IsEnabled = selectedDebt != null && !selectedDebt.IsPaid;
        }

        private void AddDebtButton_Click(object sender, RoutedEventArgs e)
        {
            var debtWindow = new DebtWindow(_client);
            if (debtWindow.ShowDialog() == true)
            {
                _ = LoadClientDebtsAsync();
            }
        }

        private async void MarkPaidButton_Click(object sender, RoutedEventArgs e)
        {
            if (DebtsDataGrid.SelectedItem is Debt selectedDebt && !selectedDebt.IsPaid)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من تسديد هذا الدين؟\nالمبلغ: {selectedDebt.Amount:N2} {selectedDebt.Currency.Symbol}",
                    "تأكيد التسديد",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _debtService.MarkDebtAsPaidAsync(selectedDebt.Id);
                        if (success)
                        {
                            MessageBox.Show("تم تسديد الدين بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                            await LoadClientDebtsAsync();
                        }
                        else
                        {
                            MessageBox.Show("فشل في تسديد الدين", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تسديد الدين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
