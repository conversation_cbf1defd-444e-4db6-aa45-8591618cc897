using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Services;
using DebtManager.Models;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace DebtManager.Views
{
    public partial class ReportsWindow : Window
    {
        private readonly DebtService _debtService;
        private readonly CurrencyService _currencyService;
        private List<Debt> _allDebts = new();
        private List<Currency> _currencies = new();
        private List<Debt> _filteredDebts = new();

        public ReportsWindow()
        {
            InitializeComponent();
            _debtService = new DebtService();
            _currencyService = new CurrencyService();
        }

        private async void ReportsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
            InitializeFilters();
            await GenerateReportAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                _allDebts = await _debtService.GetAllDebtsAsync();
                _currencies = await _currencyService.GetAllCurrenciesAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeFilters()
        {
            // Set default date range (last 30 days)
            ToDatePicker.SelectedDate = DateTime.Today;
            FromDatePicker.SelectedDate = DateTime.Today.AddDays(-30);

            // Setup currency filter
            var allCurrenciesOption = new Currency { Id = 0, Name = "جميع العملات" };
            var currencyOptions = new List<Currency> { allCurrenciesOption };
            currencyOptions.AddRange(_currencies);
            
            CurrencyFilterComboBox.ItemsSource = currencyOptions;
            CurrencyFilterComboBox.SelectedIndex = 0;
        }

        private async void GenerateReportButton_Click(object sender, RoutedEventArgs e)
        {
            await GenerateReportAsync();
        }

        private async Task GenerateReportAsync()
        {
            try
            {
                // Apply filters
                _filteredDebts = ApplyFilters();

                // Generate summary
                await GenerateSummaryAsync();

                // Generate detailed report
                DetailedReportGrid.ItemsSource = _filteredDebts.OrderByDescending(d => d.TransactionDate);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<Debt> ApplyFilters()
        {
            var filtered = _allDebts.AsEnumerable();

            // Date filter
            if (FromDatePicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(d => d.TransactionDate.Date >= FromDatePicker.SelectedDate.Value.Date);
            }

            if (ToDatePicker.SelectedDate.HasValue)
            {
                filtered = filtered.Where(d => d.TransactionDate.Date <= ToDatePicker.SelectedDate.Value.Date);
            }

            // Currency filter
            var selectedCurrency = CurrencyFilterComboBox.SelectedItem as Currency;
            if (selectedCurrency != null && selectedCurrency.Id > 0)
            {
                filtered = filtered.Where(d => d.CurrencyId == selectedCurrency.Id);
            }

            return filtered.ToList();
        }

        private async Task GenerateSummaryAsync()
        {
            // Calculate totals
            var debtsToUs = _filteredDebts
                .Where(d => d.DebtType == DebtType.ClientOwesUs || d.DebtType == DebtType.SupplierOwesUs)
                .Sum(d => d.Amount);

            var owedByUs = _filteredDebts
                .Where(d => d.DebtType == DebtType.WeOweClient || d.DebtType == DebtType.WeOweSupplier)
                .Sum(d => d.Amount);

            var netBalance = debtsToUs - owedByUs;

            var overdueDebts = _filteredDebts
                .Where(d => d.IsOverdue)
                .Sum(d => d.Amount);

            // Update summary cards
            TotalDebtsToUsText.Text = $"{debtsToUs:N2}";
            TotalOwedByUsText.Text = $"{owedByUs:N2}";
            NetBalanceText.Text = $"{netBalance:N2}";
            OverdueDebtsText.Text = $"{overdueDebts:N2}";

            // Set colors for net balance
            if (netBalance > 0)
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("CreditColor");
            }
            else if (netBalance < 0)
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("DebtColor");
            }
            else
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("NeutralColor");
            }

            // Generate summary by currency
            await GenerateSummaryByCurrencyAsync();
        }

        private async Task GenerateSummaryByCurrencyAsync()
        {
            var summaryByCurrency = _filteredDebts
                .GroupBy(d => d.Currency)
                .Select(g => new
                {
                    CurrencyName = g.Key.Name,
                    DebtsToUs = g.Where(d => d.DebtType == DebtType.ClientOwesUs || d.DebtType == DebtType.SupplierOwesUs).Sum(d => d.Amount),
                    OwedByUs = g.Where(d => d.DebtType == DebtType.WeOweClient || d.DebtType == DebtType.WeOweSupplier).Sum(d => d.Amount),
                    NetBalance = g.Where(d => d.DebtType == DebtType.ClientOwesUs || d.DebtType == DebtType.SupplierOwesUs).Sum(d => d.Amount) -
                                g.Where(d => d.DebtType == DebtType.WeOweClient || d.DebtType == DebtType.WeOweSupplier).Sum(d => d.Amount),
                    TransactionCount = g.Count()
                })
                .OrderBy(s => s.CurrencyName)
                .ToList();

            SummaryByCurrencyGrid.ItemsSource = summaryByCurrency;
        }

        private void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "PDF files (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"تقرير_الديون_{DateTime.Now:yyyy-MM-dd}.pdf"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportToPdf(saveFileDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح!", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV files (*.csv)|*.csv",
                    DefaultExt = "csv",
                    FileName = $"تقرير_الديون_{DateTime.Now:yyyy-MM-dd}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportToCsv(saveFileDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح!", "نجح التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير CSV: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToPdf(string fileName)
        {
            // Simple text-based PDF export (for now)
            var content = new StringBuilder();
            content.AppendLine("تقرير الديون والمعاملات");
            content.AppendLine("===================");
            content.AppendLine($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}");
            content.AppendLine($"الفترة: من {FromDatePicker.SelectedDate:dd/MM/yyyy} إلى {ToDatePicker.SelectedDate:dd/MM/yyyy}");
            content.AppendLine();
            
            content.AppendLine("الملخص:");
            content.AppendLine($"إجمالي المستحقات لنا: {TotalDebtsToUsText.Text}");
            content.AppendLine($"إجمالي المستحقات علينا: {TotalOwedByUsText.Text}");
            content.AppendLine($"الرصيد الصافي: {NetBalanceText.Text}");
            content.AppendLine($"الديون المتأخرة: {OverdueDebtsText.Text}");
            content.AppendLine();

            content.AppendLine("تفاصيل المعاملات:");
            content.AppendLine("التاريخ والوقت\tالشخص\tالمبلغ\tالعملة\tالنوع\tالحالة");
            
            foreach (var debt in _filteredDebts.OrderByDescending(d => d.TransactionDate))
            {
                content.AppendLine($"{debt.TransactionDate:dd/MM/yyyy HH:mm}\t{debt.PersonName}\t{debt.Amount:N2}\t{debt.Currency.Symbol}\t{debt.DebtTypeText}\t{debt.StatusText}");
            }

            File.WriteAllText(fileName, content.ToString(), Encoding.UTF8);
        }

        private void ExportToCsv(string fileName)
        {
            var content = new StringBuilder();
            
            // Header
            content.AppendLine("التاريخ والوقت,الشخص,المبلغ,العملة,النوع,العملية,الحالة,الملاحظات");
            
            // Data
            foreach (var debt in _filteredDebts.OrderByDescending(d => d.TransactionDate))
            {
                content.AppendLine($"{debt.TransactionDate:dd/MM/yyyy HH:mm},{debt.PersonName},{debt.Amount:N2},{debt.Currency.Symbol},{debt.DebtTypeText},{debt.TransactionTypeText},{debt.StatusText},\"{debt.Notes}\"");
            }

            File.WriteAllText(fileName, content.ToString(), Encoding.UTF8);
        }
    }
}
