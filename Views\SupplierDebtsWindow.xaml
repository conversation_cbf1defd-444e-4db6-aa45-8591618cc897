<Window x:Class="DebtManager.Views.SupplierDebtsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="ديون المورد" 
        Height="600" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CurrencyUsd" 
                                           Width="32" Height="32" 
                                           Foreground="White" 
                                           VerticalAlignment="Center"/>
                    <TextBlock x:Name="HeaderText"
                             Text="ديون المورد" 
                             FontSize="24" FontWeight="Bold" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             Margin="10,0,0,0"/>
                </StackPanel>
                
                <Button Grid.Column="1" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Background="White"
                        BorderBrush="White"
                        Foreground="{DynamicResource PrimaryHueMidBrush}"
                        Click="CloseButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                        <TextBlock Text="إغلاق" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>
        
        <!-- Supplier Info & Summary -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="20,20,20,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Supplier Info -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                    <TextBlock Text="المورد: " FontWeight="Bold" FontSize="16"/>
                    <TextBlock x:Name="SupplierNameText" FontSize="16" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text=" | الشركة: " FontWeight="Bold" FontSize="14" Margin="20,0,0,0"/>
                    <TextBlock x:Name="SupplierCompanyText" FontSize="14"/>
                    <TextBlock Text=" | الهاتف: " FontWeight="Bold" FontSize="14" Margin="20,0,0,0"/>
                    <TextBlock x:Name="SupplierPhoneText" FontSize="14"/>
                </StackPanel>
                
                <!-- Summary Cards -->
                <UniformGrid Grid.Row="1" Rows="1" Columns="3">
                    <!-- Total We Owe -->
                    <Border Style="{StaticResource CardStyle}" Background="#FFEBEE" Margin="5">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingDown" 
                                                       Width="20" Height="20" 
                                                       Foreground="{StaticResource DebtColor}"/>
                                <TextBlock Text="إجمالي ديوننا عليه" 
                                         FontSize="14" FontWeight="Bold" 
                                         Foreground="{StaticResource DebtColor}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TotalWeOweText" 
                                     Text="0" 
                                     FontSize="18" FontWeight="Bold" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Total Supplier Owes Us -->
                    <Border Style="{StaticResource CardStyle}" Background="#E8F5E8" Margin="5">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingUp" 
                                                       Width="20" Height="20" 
                                                       Foreground="{StaticResource ProfitColor}"/>
                                <TextBlock Text="إجمالي ديونه لنا" 
                                         FontSize="14" FontWeight="Bold" 
                                         Foreground="{StaticResource ProfitColor}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TotalSupplierOwesText" 
                                     Text="0" 
                                     FontSize="18" FontWeight="Bold" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Net Balance -->
                    <Border Style="{StaticResource CardStyle}" Background="#F3E5F5" Margin="5">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Scale" 
                                                       Width="20" Height="20" 
                                                       Foreground="#9C27B0"/>
                                <TextBlock Text="الرصيد الصافي" 
                                         FontSize="14" FontWeight="Bold" 
                                         Foreground="#9C27B0"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="NetBalanceText" 
                                     Text="0" 
                                     FontSize="18" FontWeight="Bold" 
                                     HorizontalAlignment="Center" 
                                     Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>
            </Grid>
        </Border>
        
        <!-- Debts DataGrid -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="20,0,20,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" 
                         Text="تفاصيل المعاملات" 
                         FontSize="18" FontWeight="Bold" 
                         HorizontalAlignment="Center" 
                         Margin="0,0,0,10"/>
                
                <DataGrid x:Name="DebtsDataGrid" 
                        Grid.Row="1"
                        Style="{StaticResource ModernDataGrid}"
                        SelectionChanged="DebtsDataGrid_SelectionChanged">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ والوقت"
                                          Binding="{Binding TransactionDate, StringFormat=dd/MM/yyyy HH:mm}"
                                          Width="140"/>
                        <DataGridTextColumn Header="المبلغ" 
                                          Binding="{Binding Amount, StringFormat=N2}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="العملة" 
                                          Binding="{Binding Currency.Symbol}" 
                                          Width="80"/>
                        <DataGridTextColumn Header="النوع" 
                                          Binding="{Binding DebtTypeText}" 
                                          Width="120"/>
                        <DataGridTextColumn Header="العملية" 
                                          Binding="{Binding TransactionTypeText}" 
                                          Width="80"/>
                        <DataGridTextColumn Header="تاريخ الاستحقاق" 
                                          Binding="{Binding DueDate, StringFormat=dd/MM/yyyy}" 
                                          Width="120"/>
                        <DataGridTextColumn Header="الحالة" 
                                          Binding="{Binding StatusText}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="الملاحظات" 
                                          Binding="{Binding Notes}" 
                                          Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="20">
            <Button x:Name="AddDebtButton" 
                    Style="{StaticResource PrimaryButton}"
                    Click="AddDebtButton_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Plus" Width="20" Height="20"/>
                    <TextBlock Text="إضافة معاملة" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="MarkPaidButton" 
                    Style="{StaticResource SecondaryButton}"
                    Click="MarkPaidButton_Click"
                    IsEnabled="False">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Check" Width="20" Height="20"/>
                    <TextBlock Text="تسديد" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
