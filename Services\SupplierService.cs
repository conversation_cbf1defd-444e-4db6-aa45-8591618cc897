using Microsoft.Data.Sqlite;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DebtManager.Data;
using DebtManager.Models;

namespace DebtManager.Services
{
    public class SupplierService
    {
        public async Task<List<Supplier>> GetAllSuppliersAsync()
        {
            var suppliers = new List<Supplier>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT Id, Name, Phone, Address, Company, Notes, IsActive, CreatedDate 
                         FROM Suppliers WHERE IsActive = 1 ORDER BY Name";
            
            using var command = new SqliteCommand(query, connection);
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                suppliers.Add(new Supplier
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Phone = reader.IsDBNull(reader.GetOrdinal("Phone")) ? null : reader.GetString(reader.GetOrdinal("Phone")),
                    Address = reader.IsDBNull(reader.GetOrdinal("Address")) ? null : reader.GetString(reader.GetOrdinal("Address")),
                    Company = reader.IsDBNull(reader.GetOrdinal("Company")) ? null : reader.GetString(reader.GetOrdinal("Company")),
                    Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                });
            }
            
            return suppliers;
        }

        public async Task<Supplier?> GetSupplierByIdAsync(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT Id, Name, Phone, Address, Company, Notes, IsActive, CreatedDate 
                         FROM Suppliers WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            using var reader = await command.ExecuteReaderAsync();
            
            if (await reader.ReadAsync())
            {
                return new Supplier
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Phone = reader.IsDBNull(reader.GetOrdinal("Phone")) ? null : reader.GetString(reader.GetOrdinal("Phone")),
                    Address = reader.IsDBNull(reader.GetOrdinal("Address")) ? null : reader.GetString(reader.GetOrdinal("Address")),
                    Company = reader.IsDBNull(reader.GetOrdinal("Company")) ? null : reader.GetString(reader.GetOrdinal("Company")),
                    Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                };
            }
            
            return null;
        }

        public async Task<int> AddSupplierAsync(Supplier supplier)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"INSERT INTO Suppliers (Name, Phone, Address, Company, Notes, IsActive, CreatedDate) 
                         VALUES (@name, @phone, @address, @company, @notes, @isActive, @createdDate);
                         SELECT last_insert_rowid();";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@name", supplier.Name);
            command.Parameters.AddWithValue("@phone", supplier.Phone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@address", supplier.Address ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@company", supplier.Company ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@notes", supplier.Notes ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@isActive", supplier.IsActive);
            command.Parameters.AddWithValue("@createdDate", supplier.CreatedDate);
            
            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        public async Task<bool> UpdateSupplierAsync(Supplier supplier)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"UPDATE Suppliers 
                         SET Name = @name, Phone = @phone, Address = @address, 
                             Company = @company, Notes = @notes, IsActive = @isActive 
                         WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", supplier.Id);
            command.Parameters.AddWithValue("@name", supplier.Name);
            command.Parameters.AddWithValue("@phone", supplier.Phone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@address", supplier.Address ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@company", supplier.Company ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@notes", supplier.Notes ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@isActive", supplier.IsActive);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteSupplierAsync(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = "UPDATE Suppliers SET IsActive = 0 WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        public async Task<List<Supplier>> SearchSuppliersAsync(string searchTerm)
        {
            var suppliers = new List<Supplier>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT Id, Name, Phone, Address, Company, Notes, IsActive, CreatedDate 
                         FROM Suppliers 
                         WHERE IsActive = 1 AND (Name LIKE @search OR Phone LIKE @search OR Company LIKE @search)
                         ORDER BY Name";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@search", $"%{searchTerm}%");
            
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                suppliers.Add(new Supplier
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Phone = reader.IsDBNull(reader.GetOrdinal("Phone")) ? null : reader.GetString(reader.GetOrdinal("Phone")),
                    Address = reader.IsDBNull(reader.GetOrdinal("Address")) ? null : reader.GetString(reader.GetOrdinal("Address")),
                    Company = reader.IsDBNull(reader.GetOrdinal("Company")) ? null : reader.GetString(reader.GetOrdinal("Company")),
                    Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                });
            }
            
            return suppliers;
        }
    }
}
