using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class ClientsWindow : Window
    {
        private readonly ClientService _clientService;
        private List<Client> _allClients = new();

        public ClientsWindow()
        {
            InitializeComponent();
            _clientService = new ClientService();
            Loaded += ClientsWindow_Loaded;
        }

        private async void ClientsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadClientsAsync();
        }

        private async Task LoadClientsAsync()
        {
            try
            {
                _allClients = await _clientService.GetAllClientsAsync();
                ClientsDataGrid.ItemsSource = _allClients;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchTerm = SearchTextBox.Text?.Trim();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                ClientsDataGrid.ItemsSource = _allClients;
            }
            else
            {
                try
                {
                    var filteredClients = await _clientService.SearchClientsAsync(searchTerm);
                    ClientsDataGrid.ItemsSource = filteredClients;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ClientsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var hasSelection = ClientsDataGrid.SelectedItem != null;
            EditClientButton.IsEnabled = hasSelection;
            ViewDebtsButton.IsEnabled = hasSelection;
            DeleteClientButton.IsEnabled = hasSelection;
        }

        private void ClientsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (ClientsDataGrid.SelectedItem is Client selectedClient)
            {
                EditClient(selectedClient);
            }
        }

        private void AddClientButton_Click(object sender, RoutedEventArgs e)
        {
            var clientDialog = new ClientDialog();
            if (clientDialog.ShowDialog() == true)
            {
                _ = LoadClientsAsync();
            }
        }

        private void EditClientButton_Click(object sender, RoutedEventArgs e)
        {
            if (ClientsDataGrid.SelectedItem is Client selectedClient)
            {
                EditClient(selectedClient);
            }
        }

        private void EditClient(Client client)
        {
            var clientDialog = new ClientDialog(client);
            if (clientDialog.ShowDialog() == true)
            {
                _ = LoadClientsAsync();
            }
        }

        private void ViewDebtsButton_Click(object sender, RoutedEventArgs e)
        {
            if (ClientsDataGrid.SelectedItem is Client selectedClient)
            {
                var debtsWindow = new ClientDebtsWindow(selectedClient);
                debtsWindow.ShowDialog();
            }
        }

        private async void DeleteClientButton_Click(object sender, RoutedEventArgs e)
        {
            if (ClientsDataGrid.SelectedItem is Client selectedClient)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العميل '{selectedClient.Name}'؟\nسيتم إلغاء تفعيل العميل وليس حذفه نهائياً.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _clientService.DeleteClientAsync(selectedClient.Id);
                        if (success)
                        {
                            MessageBox.Show("تم حذف العميل بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                            await LoadClientsAsync();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف العميل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void BackButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
