﻿#pragma checksum "..\..\..\..\Views\SearchWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AFFE2C63854151D96B339D8D442E33021A5D1AD9"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Views {
    
    
    /// <summary>
    /// SearchWindow
    /// </summary>
    public partial class SearchWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountFromTextBox;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountToTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateFromPicker;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateToPicker;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DebtTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PersonTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearButton;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveFilterButton;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResultsCountText;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ResultsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\Views\SearchWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportResultsButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/views/searchwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SearchWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 14 "..\..\..\..\Views\SearchWindow.xaml"
            ((DebtManager.Views.SearchWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.SearchWindow_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 58 "..\..\..\..\Views\SearchWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AmountFromTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 67 "..\..\..\..\Views\SearchWindow.xaml"
            this.AmountFromTextBox.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.AmountTextBox_PreviewTextInput);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AmountToTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 76 "..\..\..\..\Views\SearchWindow.xaml"
            this.AmountToTextBox.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.AmountTextBox_PreviewTextInput);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DateFromPicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 6:
            this.DateToPicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.CurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.DebtTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.PersonTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\Views\SearchWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ClearButton = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\..\Views\SearchWindow.xaml"
            this.ClearButton.Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.SaveFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 185 "..\..\..\..\Views\SearchWindow.xaml"
            this.SaveFilterButton.Click += new System.Windows.RoutedEventHandler(this.SaveFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ResultsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.ResultsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 217 "..\..\..\..\Views\SearchWindow.xaml"
            this.ResultsDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.ResultsDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ExportResultsButton = ((System.Windows.Controls.Button)(target));
            
            #line 236 "..\..\..\..\Views\SearchWindow.xaml"
            this.ExportResultsButton.Click += new System.Windows.RoutedEventHandler(this.ExportResultsButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

