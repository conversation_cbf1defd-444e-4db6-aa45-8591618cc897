<Window x:Class="DebtManager.Views.DebtWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة معاملة مالية"
        Height="750" Width="850"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        ResizeMode="CanResize"
        MinHeight="750" MinWidth="850">
    
    <Grid Margin="30">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="CurrencyUsd" 
                                   Width="32" Height="32" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   VerticalAlignment="Center"/>
            <TextBlock Text="إضافة معاملة مالية جديدة" 
                     FontSize="24" FontWeight="Bold" 
                     Foreground="{DynamicResource PrimaryHueMidBrush}" 
                     VerticalAlignment="Center" 
                     Margin="10,0,0,0"/>
        </StackPanel>
        
        <!-- Form -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="10">
                <StackPanel>
                <!-- Person Type Selection -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="نوع الشخص *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <StackPanel Orientation="Horizontal">
                        <RadioButton x:Name="ClientRadioButton"
                                   Content="عميل"
                                   GroupName="PersonType"
                                   IsChecked="True"
                                   Checked="PersonType_Changed"
                                   Margin="0,0,20,0"/>
                        <RadioButton x:Name="SupplierRadioButton"
                                   Content="مورد"
                                   GroupName="PersonType"
                                   Checked="PersonType_Changed"/>
                    </StackPanel>
                </StackPanel>

                <!-- Person Selection -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock x:Name="PersonLabel" Text="العميل *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="PersonComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            DisplayMemberPath="Name"
                            SelectionChanged="PersonComboBox_SelectionChanged"/>
                </StackPanel>

                <!-- Transaction Type -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="نوع المعاملة *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="TransactionTypeComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            SelectionChanged="TransactionTypeComboBox_SelectionChanged">
                        <ComboBoxItem Content="سحب" Tag="Debit"/>
                        <ComboBoxItem Content="دفع" Tag="Credit"/>
                    </ComboBox>
                </StackPanel>
                
                <!-- Debt Type Info -->
                <Border x:Name="DebtTypeInfoPanel"
                        Background="#E3F2FD"
                        CornerRadius="4"
                        Padding="10"
                        Visibility="Collapsed"
                        Margin="0,0,0,15">
                    <TextBlock x:Name="DebtTypeInfoText"
                             TextWrapping="Wrap"
                             FontSize="12"
                             Foreground="#1976D2"/>
                </Border>

                <!-- Amount -->
                <TextBox x:Name="AmountTextBox"
                         materialDesign:HintAssist.Hint="المبلغ *"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         PreviewTextInput="AmountTextBox_PreviewTextInput"
                         KeyDown="TextBox_KeyDown"
                         TabIndex="1"
                         Margin="0,0,0,20"/>

                <!-- Currency -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="العملة *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="CurrencyComboBox"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            DisplayMemberPath="Name"
                            KeyDown="ComboBox_KeyDown"
                            TabIndex="2"/>
                </StackPanel>

                <!-- Transaction Date and Time -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock Text="تاريخ ووقت المعاملة *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Text="سيتم تسجيل الوقت الحالي تلقائياً"
                             FontSize="11"
                             Foreground="Gray"
                             Margin="0,0,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <DatePicker x:Name="TransactionDatePicker"
                                  Grid.Column="0"
                                  materialDesign:HintAssist.Hint="التاريخ"
                                  Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                  TabIndex="3"/>

                        <TextBox x:Name="CurrentTimeTextBox"
                               Grid.Column="2"
                               materialDesign:HintAssist.Hint="الوقت الحالي"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               IsReadOnly="True"
                               TabIndex="4"/>
                    </Grid>
                    <TextBlock Text="💡 يمكنك تعديل الوقت إذا أردت وقتاً مختلفاً"
                             FontSize="10"
                             Foreground="DarkBlue"
                             Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Due Date -->
                <DatePicker x:Name="DueDatePicker"
                          materialDesign:HintAssist.Hint="تاريخ الاستحقاق (اختياري)"
                          Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                          TabIndex="5"
                          Margin="0,0,0,20"/>

                <!-- Payment Reminder -->
                <StackPanel Margin="0,0,0,20">
                    <CheckBox x:Name="EnableReminderCheckBox"
                            Content="تفعيل تنبيه السداد"
                            FontWeight="Bold"
                            Checked="EnableReminderCheckBox_Checked"
                            Unchecked="EnableReminderCheckBox_Unchecked"
                            TabIndex="6"
                            Margin="0,0,0,10"/>

                    <StackPanel x:Name="ReminderOptionsPanel"
                              Visibility="Collapsed"
                              Margin="20,0,0,0">
                        <TextBlock Text="تنبيه قبل تاريخ الاستحقاق بـ:"
                                 FontSize="12"
                                 Margin="0,0,0,5"/>
                        <ComboBox x:Name="ReminderDaysComboBox"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                TabIndex="7">
                            <ComboBoxItem Content="يوم واحد" Tag="1"/>
                            <ComboBoxItem Content="3 أيام" Tag="3"/>
                            <ComboBoxItem Content="أسبوع" Tag="7" IsSelected="True"/>
                            <ComboBoxItem Content="أسبوعين" Tag="14"/>
                            <ComboBoxItem Content="شهر" Tag="30"/>
                        </ComboBox>
                    </StackPanel>
                </StackPanel>

                <!-- Notes -->
                <TextBox x:Name="NotesTextBox"
                         materialDesign:HintAssist.Hint="ملاحظات"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="500"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         TabIndex="8"
                         KeyDown="TextBox_KeyDown"
                         Margin="0,0,0,15"/>

                <!-- Required Field Note -->
                <TextBlock Text="* الحقول المطلوبة"
                         FontSize="12"
                         Foreground="Gray"
                         HorizontalAlignment="Right"/>
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            <Button x:Name="SaveButton" 
                    Style="{StaticResource PrimaryButton}"
                    Click="SaveButton_Click"
                    Width="120">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20"/>
                    <TextBlock Text="حفظ" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="CancelButton" 
                    Style="{StaticResource SecondaryButton}"
                    Click="CancelButton_Click"
                    Width="120">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cancel" Width="20" Height="20"/>
                    <TextBlock Text="إلغاء" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
