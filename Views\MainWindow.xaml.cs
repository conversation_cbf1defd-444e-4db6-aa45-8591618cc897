using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class MainWindow : Window
    {
        private readonly DebtService _debtService;
        private readonly ClientService _clientService;
        private readonly SupplierService _supplierService;

        public MainWindow()
        {
            InitializeComponent();
            _debtService = new DebtService();
            _clientService = new ClientService();
            _supplierService = new SupplierService();
            
            Loaded += MainWindow_Loaded;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            CurrentDateText.Text = DateTime.Now.ToString("dddd، dd MMMM yyyy", new System.Globalization.CultureInfo("ar-SA"));
            await LoadDashboardDataAsync();

            // Load notifications and show startup alerts
            await UpdateNotificationBadgeAsync();
            await ShowStartupNotificationsAsync();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                // Load all debts
                var allDebts = await _debtService.GetAllDebtsAsync();
                var clients = await _clientService.GetAllClientsAsync();
                var suppliers = await _supplierService.GetAllSuppliersAsync();

                // Calculate totals by currency
                var unpaidDebts = allDebts.Where(d => !d.IsPaid).ToList();
                var currencySummary = CalculateCurrencySummary(unpaidDebts);

                var overdueDebts = await _debtService.GetOverdueDebtsAsync();

                // Update summary cards with currency breakdown
                UpdateSummaryCards(currencySummary);
                OverdueDebtsText.Text = overdueDebts.Count.ToString();
                TotalPeopleText.Text = (clients.Count + suppliers.Count).ToString();

                // Load recent transactions (last 10)
                RecentTransactionsGrid.ItemsSource = allDebts.Take(10).ToList();

                // Show alerts if there are overdue debts
                if (overdueDebts.Any())
                {
                    ShowAlerts(overdueDebts);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowAlerts(List<Debt> overdueDebts)
        {
            var alerts = new List<string>();
            
            foreach (var debt in overdueDebts.Take(5)) // Show only first 5 alerts
            {
                var daysOverdue = (DateTime.Today - debt.DueDate!.Value).Days;
                alerts.Add($"{debt.PersonName}: {debt.Amount:N2} {debt.Currency.Symbol} - متأخر {daysOverdue} يوم");
            }

            if (overdueDebts.Count > 5)
            {
                alerts.Add($"و {overdueDebts.Count - 5} ديون أخرى متأخرة...");
            }

            AlertsList.ItemsSource = alerts;
            AlertsSection.Visibility = Visibility.Visible;
        }

        private void ClientsButton_Click(object sender, RoutedEventArgs e)
        {
            var clientsWindow = new ClientsWindow();
            clientsWindow.ShowDialog();
            // Refresh dashboard after closing clients window
            _ = LoadDashboardDataAsync();
        }

        private void SuppliersButton_Click(object sender, RoutedEventArgs e)
        {
            var suppliersWindow = new SuppliersWindow();
            suppliersWindow.ShowDialog();
            // Refresh dashboard after closing suppliers window
            _ = LoadDashboardDataAsync();
        }

        private void AddDebtButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var debtWindow = new DebtWindow();
                debtWindow.ShowDialog();
                // Refresh dashboard after closing debt window
                _ = LoadDashboardDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة الدين: {ex.Message}\n\nتفاصيل:\n{ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ReportsButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            var searchWindow = new SearchWindow();
            searchWindow.ShowDialog();
        }

        private void NotificationsButton_Click(object sender, RoutedEventArgs e)
        {
            var notificationsWindow = new NotificationsWindow();
            notificationsWindow.ShowDialog();

            // Update notification badge after viewing
            _ = UpdateNotificationBadgeAsync();
        }

        private void CurrenciesButton_Click(object sender, RoutedEventArgs e)
        {
            var currenciesWindow = new CurrenciesWindow();
            currenciesWindow.ShowDialog();
        }

        private Dictionary<string, CurrencyBalance> CalculateCurrencySummary(List<Debt> unpaidDebts)
        {
            var summary = new Dictionary<string, CurrencyBalance>();

            foreach (var debt in unpaidDebts)
            {
                var currencyKey = $"{debt.Currency.Name} ({debt.Currency.Symbol})";

                if (!summary.ContainsKey(currencyKey))
                {
                    summary[currencyKey] = new CurrencyBalance
                    {
                        CurrencyName = debt.Currency.Name,
                        CurrencySymbol = debt.Currency.Symbol,
                        ForUs = 0,
                        OnUs = 0
                    };
                }

                if (debt.DebtType == DebtType.ClientOwesUs || debt.DebtType == DebtType.SupplierOwesUs)
                {
                    summary[currencyKey].ForUs += debt.Amount;
                }
                else if (debt.DebtType == DebtType.WeOweClient || debt.DebtType == DebtType.WeOweSupplier)
                {
                    summary[currencyKey].OnUs += debt.Amount;
                }
            }

            return summary;
        }

        private void UpdateSummaryCards(Dictionary<string, CurrencyBalance> currencySummary)
        {
            var forUsText = "";
            var onUsText = "";

            foreach (var currency in currencySummary.Values)
            {
                var netBalance = currency.ForUs - currency.OnUs;

                if (netBalance > 0)
                {
                    // لنا - أخضر
                    forUsText += $"{netBalance:N0} {currency.CurrencySymbol}\n";
                }
                else if (netBalance < 0)
                {
                    // علينا - أحمر مع إشارة سالب
                    onUsText += $"- {Math.Abs(netBalance):N0} {currency.CurrencySymbol}\n";
                }
                // إذا كان الرصيد صفر، لا نعرض شيء
            }

            // تحديث النصوص
            TotalDebtsForUsText.Text = string.IsNullOrEmpty(forUsText) ? "0" : forUsText.Trim();
            TotalDebtsOnUsText.Text = string.IsNullOrEmpty(onUsText) ? "0" : onUsText.Trim();

            // تطبيق الألوان
            if (!string.IsNullOrEmpty(forUsText))
            {
                TotalDebtsForUsText.Foreground = (System.Windows.Media.Brush)FindResource("ProfitColor");
            }

            if (!string.IsNullOrEmpty(onUsText))
            {
                TotalDebtsOnUsText.Foreground = (System.Windows.Media.Brush)FindResource("DebtColor");
            }
        }

        private async Task UpdateNotificationBadgeAsync()
        {
            try
            {
                var notificationService = new Services.NotificationService();
                var notifications = await notificationService.GetPendingNotificationsAsync();
                var highPriorityCount = notifications.Count(n => n.Priority == Services.NotificationPriority.High);

                if (highPriorityCount > 0)
                {
                    NotificationBadge.Visibility = Visibility.Visible;
                    NotificationCountText.Text = highPriorityCount > 99 ? "99+" : highPriorityCount.ToString();
                }
                else
                {
                    NotificationBadge.Visibility = Visibility.Collapsed;
                }
            }
            catch
            {
                // Hide badge on error
                NotificationBadge.Visibility = Visibility.Collapsed;
            }
        }

        private async Task ShowStartupNotificationsAsync()
        {
            try
            {
                var notificationService = new Services.NotificationService();
                await notificationService.ShowStartupNotificationsAsync();
            }
            catch
            {
                // Ignore errors to avoid breaking app startup
            }
        }
    }
}
