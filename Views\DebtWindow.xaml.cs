using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Text.RegularExpressions;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class DebtWindow : Window
    {
        private readonly DebtService _debtService;
        private readonly ClientService _clientService;
        private readonly SupplierService _supplierService;
        private readonly CurrencyService _currencyService;
        
        private readonly Client? _preselectedClient;
        private readonly Supplier? _preselectedSupplier;
        
        private List<Client> _clients = new();
        private List<Supplier> _suppliers = new();
        private List<Currency> _currencies = new();

        public DebtWindow(Client? client = null, Supplier? supplier = null)
        {
            try
            {
                InitializeComponent();

                _debtService = new DebtService();
                _clientService = new ClientService();
                _supplierService = new SupplierService();
                _currencyService = new CurrencyService();

                _preselectedClient = client;
                _preselectedSupplier = supplier;

                Loaded += DebtWindow_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء نافذة الدين: {ex.Message}\n\nتفاصيل:\n{ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async void DebtWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadDataAsync();
                InitializeForm();
                SetCurrentTimeAutomatically();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل نافذة الدين: {ex.Message}\n\nتفاصيل:\n{ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                Close();
            }
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // Load clients
                try
                {
                    _clients = await _clientService.GetAllClientsAsync();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    _clients = new List<Client>();
                }

                // Load suppliers
                try
                {
                    _suppliers = await _supplierService.GetAllSuppliersAsync();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    _suppliers = new List<Supplier>();
                }

                // Load currencies
                try
                {
                    _currencies = await _currencyService.GetAllCurrenciesAsync();
                    CurrencyComboBox.ItemsSource = _currencies;
                    if (_currencies.Any())
                    {
                        CurrencyComboBox.SelectedIndex = 0;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل العملات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    _currencies = new List<Currency>();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ عام في تحميل البيانات: {ex.Message}\n\nتفاصيل:\n{ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private void InitializeForm()
        {
            // Set default transaction type
            TransactionTypeComboBox.SelectedIndex = 0; // Debit

            // Set default transaction date and time
            TransactionDatePicker.SelectedDate = DateTime.Today;

            // Set current time automatically (will be set in SetCurrentTimeAutomatically)

            // Pre-select person if provided
            if (_preselectedClient != null)
            {
                ClientRadioButton.IsChecked = true;
                PersonType_Changed(null, null);
                PersonComboBox.SelectedItem = _preselectedClient;
            }
            else if (_preselectedSupplier != null)
            {
                SupplierRadioButton.IsChecked = true;
                PersonType_Changed(null, null);
                PersonComboBox.SelectedItem = _preselectedSupplier;
            }
            else
            {
                PersonType_Changed(null, null);
            }
        }

        private void PersonType_Changed(object? sender, RoutedEventArgs? e)
        {
            // تجاهل الاستدعاء إذا لم يتم تحميل النافذة بعد
            if (!IsLoaded) return;

            try
            {
                if (ClientRadioButton?.IsChecked == true)
                {
                    PersonLabel.Text = "العميل *";
                    PersonComboBox.ItemsSource = _clients;
                }
                else
                {
                    PersonLabel.Text = "المورد *";
                    PersonComboBox.ItemsSource = _suppliers;
                }

                PersonComboBox.SelectedIndex = -1;
                UpdateDebtTypeInfo();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in PersonType_Changed: {ex.Message}");
            }
        }

        private void PersonComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateDebtTypeInfo();
        }

        private void TransactionTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateDebtTypeInfo();
        }

        private void UpdateDebtTypeInfo()
        {
            // تجاهل الاستدعاء إذا لم يتم تحميل النافذة بعد
            if (!IsLoaded) return;

            try
            {
                if (PersonComboBox?.SelectedItem == null || TransactionTypeComboBox?.SelectedItem == null)
                {
                    DebtTypeInfoPanel.Visibility = Visibility.Collapsed;
                    return;
                }

                var isClient = ClientRadioButton.IsChecked == true;
                var isDebit = ((ComboBoxItem)TransactionTypeComboBox.SelectedItem).Tag.ToString() == "Debit";

                string infoText;
                if (isClient)
                {
                    if (isDebit)
                    {
                        infoText = "سحب من العميل - العميل مدين لنا (أخضر)";
                    }
                    else
                    {
                        infoText = "دفع للعميل - نحن مدينون للعميل (أحمر)";
                    }
                }
                else
                {
                    if (isDebit)
                    {
                        infoText = "سحب من المورد - المورد مدين لنا (أخضر)";
                    }
                    else
                    {
                        infoText = "دفع للمورد - نحن مدينون للمورد (أحمر)";
                    }
                }

                DebtTypeInfoText.Text = infoText;
                DebtTypeInfoPanel.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateDebtTypeInfo: {ex.Message}");
            }
        }

        private void AmountTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // Allow only numbers and decimal point
            var regex = new Regex(@"^[0-9]*\.?[0-9]*$");
            e.Handled = !regex.IsMatch(AmountTextBox.Text + e.Text);
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                SaveButton.IsEnabled = false;

                var debt = CreateDebtFromForm();
                if (debt == null)
                {
                    MessageBox.Show("خطأ في إنشاء بيانات المعاملة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var newId = await _debtService.AddDebtAsync(debt);

                if (newId > 0)
                {
                    MessageBox.Show("تم إضافة المعاملة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة المعاملة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المعاملة: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }

        private bool ValidateForm()
        {
            // Validate person selection
            if (PersonComboBox.SelectedItem == null)
            {
                var personType = ClientRadioButton.IsChecked == true ? "العميل" : "المورد";
                MessageBox.Show($"يرجى اختيار {personType}", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PersonComboBox.Focus();
                return false;
            }

            // Validate transaction type
            if (TransactionTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع المعاملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                TransactionTypeComboBox.Focus();
                return false;
            }

            // Validate amount
            if (string.IsNullOrWhiteSpace(AmountTextBox.Text) || !decimal.TryParse(AmountTextBox.Text, out var amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح أكبر من الصفر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AmountTextBox.Focus();
                return false;
            }

            // Validate currency
            if (CurrencyComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار العملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CurrencyComboBox.Focus();
                return false;
            }

            // Validate transaction date
            if (!TransactionDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ المعاملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                TransactionDatePicker.Focus();
                return false;
            }

            // Validate due date (if provided, should be after transaction date)
            if (DueDatePicker.SelectedDate.HasValue && DueDatePicker.SelectedDate.Value < TransactionDatePicker.SelectedDate.Value)
            {
                MessageBox.Show("تاريخ الاستحقاق يجب أن يكون بعد تاريخ المعاملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                DueDatePicker.Focus();
                return false;
            }

            return true;
        }

        private Debt? CreateDebtFromForm()
        {
            try
            {
                var isClient = ClientRadioButton.IsChecked == true;
                var isDebit = ((ComboBoxItem)TransactionTypeComboBox.SelectedItem).Tag.ToString() == "Debit";
                var selectedCurrency = (Currency)CurrencyComboBox.SelectedItem;
                var amount = decimal.Parse(AmountTextBox.Text);

                // Determine debt type based on person type and transaction type
                DebtType debtType;
                if (isClient)
                {
                    debtType = isDebit ? DebtType.ClientOwesUs : DebtType.WeOweClient;
                }
                else
                {
                    debtType = isDebit ? DebtType.SupplierOwesUs : DebtType.WeOweSupplier;
                }

                // Combine date with current time automatically
                var transactionDateTime = TransactionDatePicker.SelectedDate!.Value;

                // Add current time automatically
                transactionDateTime = transactionDateTime.Date.Add(DateTime.Now.TimeOfDay);

                var debt = new Debt
                {
                    ClientId = isClient ? ((Client)PersonComboBox.SelectedItem).Id : null,
                    SupplierId = !isClient ? ((Supplier)PersonComboBox.SelectedItem).Id : null,
                    CurrencyId = selectedCurrency.Id,
                    Amount = amount,
                    DebtType = debtType,
                    TransactionType = isDebit ? TransactionType.Debit : TransactionType.Credit,
                    TransactionDate = transactionDateTime,
                    DueDate = DueDatePicker.SelectedDate,
                    Notes = string.IsNullOrWhiteSpace(NotesTextBox.Text) ? null : NotesTextBox.Text.Trim(),
                    IsActive = true,
                    IsPaid = false,
                    CreatedDate = DateTime.Now,
                    CreatedBy = Environment.UserName
                };

                return debt;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء بيانات المعاملة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // Navigation with Enter key
        private void TextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                var textBox = sender as TextBox;
                textBox?.MoveFocus(new TraversalRequest(FocusNavigationDirection.Next));
            }
        }

        private void ComboBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                var comboBox = sender as ComboBox;
                comboBox?.MoveFocus(new TraversalRequest(FocusNavigationDirection.Next));
            }
        }

        // Reminder checkbox handlers
        private void EnableReminderCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            if (ReminderOptionsPanel != null)
                ReminderOptionsPanel.Visibility = Visibility.Visible;
        }

        private void EnableReminderCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            if (ReminderOptionsPanel != null)
                ReminderOptionsPanel.Visibility = Visibility.Collapsed;
        }

        private void SetCurrentTimeAutomatically()
        {
            try
            {
                // Set current time in the text box
                if (CurrentTimeTextBox != null)
                {
                    CurrentTimeTextBox.Text = DateTime.Now.ToString("HH:mm:ss");
                }
                System.Diagnostics.Debug.WriteLine($"Current time set automatically: {DateTime.Now:HH:mm:ss}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Could not set time automatically: {ex.Message}");
            }
        }
    }
}
