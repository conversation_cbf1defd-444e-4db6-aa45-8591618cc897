using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class SupplierDebtsWindow : Window
    {
        private readonly Supplier _supplier;
        private readonly DebtService _debtService;
        private List<Debt> _supplierDebts = new();

        public SupplierDebtsWindow(Supplier supplier)
        {
            InitializeComponent();
            _supplier = supplier;
            _debtService = new DebtService();
            
            InitializeWindow();
            Loaded += SupplierDebtsWindow_Loaded;
        }

        private void InitializeWindow()
        {
            HeaderText.Text = $"ديون المورد: {_supplier.Name}";
            Title = $"ديون المورد: {_supplier.Name}";
            SupplierNameText.Text = _supplier.Name;
            SupplierCompanyText.Text = _supplier.Company ?? "غير محدد";
            SupplierPhoneText.Text = _supplier.Phone ?? "غير محدد";
        }

        private async void SupplierDebtsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSupplierDebtsAsync();
        }

        private async Task LoadSupplierDebtsAsync()
        {
            try
            {
                _supplierDebts = await _debtService.GetDebtsBySupplierIdAsync(_supplier.Id);
                DebtsDataGrid.ItemsSource = _supplierDebts;
                
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل ديون المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSummary()
        {
            var activeDebts = _supplierDebts.Where(d => d.IsActive && !d.IsPaid).ToList();

            // Calculate totals by currency
            var currencySummary = new Dictionary<string, CurrencyBalance>();

            foreach (var debt in activeDebts)
            {
                var currencyKey = $"{debt.Currency.Name} ({debt.Currency.Symbol})";

                if (!currencySummary.ContainsKey(currencyKey))
                {
                    currencySummary[currencyKey] = new CurrencyBalance
                    {
                        CurrencyName = debt.Currency.Name,
                        CurrencySymbol = debt.Currency.Symbol,
                        ForUs = 0,
                        OnUs = 0
                    };
                }

                if (debt.DebtType == DebtType.SupplierOwesUs)
                {
                    currencySummary[currencyKey].ForUs += debt.Amount;
                }
                else if (debt.DebtType == DebtType.WeOweSupplier)
                {
                    currencySummary[currencyKey].OnUs += debt.Amount;
                }
            }

            // Build summary text
            var weOweText = "";
            var supplierOwesText = "";
            var netText = "";
            var hasPositiveBalance = false;
            var hasNegativeBalance = false;

            foreach (var currency in currencySummary.Values)
            {
                var netBalance = currency.ForUs - currency.OnUs;

                if (currency.OnUs > 0)
                    weOweText += $"{currency.OnUs:N0} {currency.CurrencySymbol}\n";

                if (currency.ForUs > 0)
                    supplierOwesText += $"{currency.ForUs:N0} {currency.CurrencySymbol}\n";

                if (netBalance > 0)
                {
                    netText += $"{netBalance:N0} {currency.CurrencySymbol}\n";
                    hasPositiveBalance = true;
                }
                else if (netBalance < 0)
                {
                    netText += $"- {Math.Abs(netBalance):N0} {currency.CurrencySymbol}\n";
                    hasNegativeBalance = true;
                }
            }

            TotalWeOweText.Text = string.IsNullOrEmpty(weOweText) ? "0" : weOweText.Trim();
            TotalSupplierOwesText.Text = string.IsNullOrEmpty(supplierOwesText) ? "0" : supplierOwesText.Trim();
            NetBalanceText.Text = string.IsNullOrEmpty(netText) ? "0" : netText.Trim();

            // Apply colors to all text elements
            TotalWeOweText.Foreground = (System.Windows.Media.Brush)FindResource("DebtColor");
            TotalSupplierOwesText.Foreground = (System.Windows.Media.Brush)FindResource("ProfitColor");

            // Color coding for net balance
            if (hasPositiveBalance && !hasNegativeBalance)
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("ProfitColor");
            }
            else if (hasNegativeBalance && !hasPositiveBalance)
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("DebtColor");
            }
            else
            {
                NetBalanceText.Foreground = (System.Windows.Media.Brush)FindResource("NeutralColor");
            }
        }

        private void DebtsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedDebt = DebtsDataGrid.SelectedItem as Debt;
            MarkPaidButton.IsEnabled = selectedDebt != null && !selectedDebt.IsPaid;
        }

        private void AddDebtButton_Click(object sender, RoutedEventArgs e)
        {
            var debtWindow = new DebtWindow(null, _supplier);
            if (debtWindow.ShowDialog() == true)
            {
                _ = LoadSupplierDebtsAsync();
            }
        }

        private async void MarkPaidButton_Click(object sender, RoutedEventArgs e)
        {
            if (DebtsDataGrid.SelectedItem is Debt selectedDebt && !selectedDebt.IsPaid)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من تسديد هذا الدين؟\nالمبلغ: {selectedDebt.Amount:N2} {selectedDebt.Currency.Symbol}",
                    "تأكيد التسديد",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _debtService.MarkDebtAsPaidAsync(selectedDebt.Id);
                        if (success)
                        {
                            MessageBox.Show("تم تسديد الدين بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                            await LoadSupplierDebtsAsync();
                        }
                        else
                        {
                            MessageBox.Show("فشل في تسديد الدين", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تسديد الدين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
