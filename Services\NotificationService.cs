using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using DebtManager.Models;

namespace DebtManager.Services
{
    public class NotificationService
    {
        private readonly DebtService _debtService;

        public NotificationService()
        {
            _debtService = new DebtService();
        }

        public async Task<List<NotificationItem>> GetPendingNotificationsAsync()
        {
            var notifications = new List<NotificationItem>();
            
            try
            {
                var allDebts = await _debtService.GetAllDebtsAsync();
                var unpaidDebts = allDebts.Where(d => !d.IsPaid && d.IsActive).ToList();

                // Overdue debts
                var overdueDebts = unpaidDebts.Where(d => d.IsOverdue).ToList();
                foreach (var debt in overdueDebts)
                {
                    notifications.Add(new NotificationItem
                    {
                        Id = Guid.NewGuid(),
                        Type = NotificationType.Overdue,
                        Title = "دين متأخر",
                        Message = $"الدين مع {debt.PersonName} متأخر منذ {Math.Abs((debt.DueDate!.Value - DateTime.Today).Days)} يوم",
                        DebtId = debt.Id,
                        PersonName = debt.PersonName,
                        Amount = debt.Amount,
                        Currency = debt.Currency.Symbol,
                        DueDate = debt.DueDate,
                        Priority = NotificationPriority.High,
                        CreatedDate = DateTime.Now
                    });
                }

                // Due soon debts (within 7 days)
                var dueSoonDebts = unpaidDebts.Where(d => 
                    d.DueDate.HasValue && 
                    !d.IsOverdue && 
                    d.DaysUntilDue <= 7 && 
                    d.DaysUntilDue >= 0).ToList();

                foreach (var debt in dueSoonDebts)
                {
                    notifications.Add(new NotificationItem
                    {
                        Id = Guid.NewGuid(),
                        Type = NotificationType.DueSoon,
                        Title = "دين مستحق قريباً",
                        Message = $"الدين مع {debt.PersonName} مستحق خلال {debt.DaysUntilDue} يوم",
                        DebtId = debt.Id,
                        PersonName = debt.PersonName,
                        Amount = debt.Amount,
                        Currency = debt.Currency.Symbol,
                        DueDate = debt.DueDate,
                        Priority = debt.DaysUntilDue <= 3 ? NotificationPriority.High : NotificationPriority.Medium,
                        CreatedDate = DateTime.Now
                    });
                }

                // Large amount debts (over 10,000 in any currency)
                var largeDebts = unpaidDebts.Where(d => d.Amount >= 10000).ToList();
                foreach (var debt in largeDebts)
                {
                    notifications.Add(new NotificationItem
                    {
                        Id = Guid.NewGuid(),
                        Type = NotificationType.LargeAmount,
                        Title = "دين بمبلغ كبير",
                        Message = $"دين بمبلغ كبير مع {debt.PersonName}: {debt.Amount:N0} {debt.Currency.Symbol}",
                        DebtId = debt.Id,
                        PersonName = debt.PersonName,
                        Amount = debt.Amount,
                        Currency = debt.Currency.Symbol,
                        DueDate = debt.DueDate,
                        Priority = NotificationPriority.Medium,
                        CreatedDate = DateTime.Now
                    });
                }

                // Summary notification if there are many overdue debts
                if (overdueDebts.Count >= 5)
                {
                    notifications.Add(new NotificationItem
                    {
                        Id = Guid.NewGuid(),
                        Type = NotificationType.Summary,
                        Title = "تنبيه عام",
                        Message = $"لديك {overdueDebts.Count} ديون متأخرة تحتاج لمتابعة",
                        Priority = NotificationPriority.High,
                        CreatedDate = DateTime.Now
                    });
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw to avoid breaking the app
                System.Diagnostics.Debug.WriteLine($"Error getting notifications: {ex.Message}");
            }

            return notifications.OrderByDescending(n => n.Priority).ThenByDescending(n => n.CreatedDate).ToList();
        }

        public void ShowDesktopNotification(NotificationItem notification)
        {
            try
            {
                // Simple message box notification (can be enhanced with toast notifications)
                var icon = notification.Priority switch
                {
                    NotificationPriority.High => MessageBoxImage.Warning,
                    NotificationPriority.Medium => MessageBoxImage.Information,
                    _ => MessageBoxImage.Information
                };

                MessageBox.Show(notification.Message, notification.Title, MessageBoxButton.OK, icon);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing notification: {ex.Message}");
            }
        }

        public async Task ShowStartupNotificationsAsync()
        {
            try
            {
                var notifications = await GetPendingNotificationsAsync();
                var highPriorityNotifications = notifications.Where(n => n.Priority == NotificationPriority.High).ToList();

                if (highPriorityNotifications.Any())
                {
                    var message = "تنبيهات مهمة:\n\n";
                    foreach (var notification in highPriorityNotifications.Take(5)) // Show max 5
                    {
                        message += $"• {notification.Message}\n";
                    }

                    if (highPriorityNotifications.Count > 5)
                    {
                        message += $"\n... و {highPriorityNotifications.Count - 5} تنبيهات أخرى";
                    }

                    MessageBox.Show(message, "تنبيهات مهمة", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing startup notifications: {ex.Message}");
            }
        }
    }

    public class NotificationItem
    {
        public Guid Id { get; set; }
        public NotificationType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public int? DebtId { get; set; }
        public string? PersonName { get; set; }
        public decimal? Amount { get; set; }
        public string? Currency { get; set; }
        public DateTime? DueDate { get; set; }
        public NotificationPriority Priority { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsRead { get; set; }
    }

    public enum NotificationType
    {
        Overdue,
        DueSoon,
        LargeAmount,
        Summary,
        Custom
    }

    public enum NotificationPriority
    {
        Low,
        Medium,
        High
    }
}
