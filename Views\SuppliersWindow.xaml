<Window x:Class="DebtManager.Views.SuppliersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إدارة الموردين" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="TruckDelivery" 
                                           Width="32" Height="32" 
                                           Foreground="White" 
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="إدارة الموردين" 
                             FontSize="24" FontWeight="Bold" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             Margin="10,0,0,0"/>
                </StackPanel>
                
                <Button Grid.Column="1" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Background="White"
                        BorderBrush="White"
                        Foreground="{DynamicResource PrimaryHueMidBrush}"
                        Click="BackButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ArrowRight" Width="20" Height="20"/>
                        <TextBlock Text="العودة" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>
        
        <!-- Toolbar -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="20,20,20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Search -->
                <TextBox x:Name="SearchTextBox" 
                         Grid.Column="0"
                         materialDesign:HintAssist.Hint="البحث عن مورد..."
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Margin="0,0,10,0"
                         TextChanged="SearchTextBox_TextChanged"/>
                
                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="AddSupplierButton" 
                            Style="{StaticResource PrimaryButton}"
                            Click="AddSupplierButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="20" Height="20"/>
                            <TextBlock Text="إضافة مورد" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="EditSupplierButton" 
                            Style="{StaticResource SecondaryButton}"
                            Click="EditSupplierButton_Click"
                            IsEnabled="False">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Edit" Width="20" Height="20"/>
                            <TextBlock Text="تعديل" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="ViewDebtsButton" 
                            Style="{StaticResource SecondaryButton}"
                            Click="ViewDebtsButton_Click"
                            IsEnabled="False">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20"/>
                            <TextBlock Text="عرض الديون" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button x:Name="DeleteSupplierButton" 
                            Style="{StaticResource SecondaryButton}"
                            Click="DeleteSupplierButton_Click"
                            IsEnabled="False"
                            Foreground="{StaticResource DebtColor}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Delete" Width="20" Height="20"/>
                            <TextBlock Text="حذف" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Suppliers DataGrid -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="20,0,20,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" 
                         Text="قائمة الموردين" 
                         FontSize="18" FontWeight="Bold" 
                         HorizontalAlignment="Center" 
                         Margin="0,0,0,10"/>
                
                <DataGrid x:Name="SuppliersDataGrid" 
                        Grid.Row="1"
                        Style="{StaticResource ModernDataGrid}"
                        SelectionChanged="SuppliersDataGrid_SelectionChanged"
                        MouseDoubleClick="SuppliersDataGrid_MouseDoubleClick">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرقم" 
                                          Binding="{Binding Id}" 
                                          Width="80"/>
                        <DataGridTextColumn Header="اسم المورد" 
                                          Binding="{Binding Name}" 
                                          Width="180"/>
                        <DataGridTextColumn Header="الشركة" 
                                          Binding="{Binding Company}" 
                                          Width="150"/>
                        <DataGridTextColumn Header="رقم الهاتف" 
                                          Binding="{Binding Phone}" 
                                          Width="120"/>
                        <DataGridTextColumn Header="العنوان" 
                                          Binding="{Binding Address}" 
                                          Width="180"/>
                        <DataGridTextColumn Header="تاريخ الإضافة"
                                          Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}"
                                          Width="140"/>
                        <DataGridTextColumn Header="الملاحظات" 
                                          Binding="{Binding Notes}" 
                                          Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</Window>
