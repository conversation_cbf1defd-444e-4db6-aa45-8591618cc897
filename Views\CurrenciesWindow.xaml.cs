using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class CurrenciesWindow : Window
    {
        private readonly CurrencyService _currencyService;
        private List<Currency> _currencies = new();

        public CurrenciesWindow()
        {
            InitializeComponent();
            _currencyService = new CurrencyService();
            Loaded += CurrenciesWindow_Loaded;
        }

        private async void CurrenciesWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadCurrenciesAsync();
        }

        private async Task LoadCurrenciesAsync()
        {
            try
            {
                _currencies = await _currencyService.GetAllCurrenciesAsync();
                CurrenciesDataGrid.ItemsSource = _currencies;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CurrenciesDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var hasSelection = CurrenciesDataGrid.SelectedItem != null;
            EditCurrencyButton.IsEnabled = hasSelection;
            DeleteCurrencyButton.IsEnabled = hasSelection;
        }

        private void CurrenciesDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (CurrenciesDataGrid.SelectedItem is Currency selectedCurrency)
            {
                EditCurrency(selectedCurrency);
            }
        }

        private void AddCurrencyButton_Click(object sender, RoutedEventArgs e)
        {
            var currencyDialog = new CurrencyDialog();
            if (currencyDialog.ShowDialog() == true)
            {
                _ = LoadCurrenciesAsync();
            }
        }

        private void EditCurrencyButton_Click(object sender, RoutedEventArgs e)
        {
            if (CurrenciesDataGrid.SelectedItem is Currency selectedCurrency)
            {
                EditCurrency(selectedCurrency);
            }
        }

        private void EditCurrency(Currency currency)
        {
            var currencyDialog = new CurrencyDialog(currency);
            if (currencyDialog.ShowDialog() == true)
            {
                _ = LoadCurrenciesAsync();
            }
        }

        private async void DeleteCurrencyButton_Click(object sender, RoutedEventArgs e)
        {
            if (CurrenciesDataGrid.SelectedItem is Currency selectedCurrency)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العملة '{selectedCurrency.Name}'؟\nسيتم إلغاء تفعيل العملة وليس حذفها نهائياً.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _currencyService.DeleteCurrencyAsync(selectedCurrency.Id);
                        if (success)
                        {
                            MessageBox.Show("تم حذف العملة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                            await LoadCurrenciesAsync();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف العملة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العملة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
