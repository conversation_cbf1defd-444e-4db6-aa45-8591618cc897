using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace DebtManager.Models
{
    public class Client
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [StringLength(200)]
        public string? Address { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public bool IsActive { get; set; } = true;
        
        // Navigation property
        public virtual ICollection<Debt> Debts { get; set; } = new List<Debt>();
        
        // Calculated properties
        public decimal TotalDebtAmount => Debts?.Where(d => d.DebtType == DebtType.ClientOwesUs && d.IsActive).Sum(d => d.Amount) ?? 0;
        public decimal TotalCreditAmount => Debts?.Where(d => d.DebtType == DebtType.WeOweClient && d.IsActive).Sum(d => d.Amount) ?? 0;
        public decimal NetBalance => TotalDebtAmount - TotalCreditAmount;
        
        public override string ToString()
        {
            return Name;
        }
    }
}
