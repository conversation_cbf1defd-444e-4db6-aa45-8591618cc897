# Fix reader.GetXXX methods to use GetOrdinal
Write-Host "Fixing reader methods..."

$serviceFiles = @(
    "Services\ClientService.cs",
    "Services\SupplierService.cs", 
    "Services\CurrencyService.cs",
    "Services\DebtService.cs"
)

foreach ($file in $serviceFiles) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        
        # Fix GetInt32, GetString, GetBoolean, GetDateTime, GetDecimal methods
        $content = $content -replace 'reader\.GetInt32\("([^"]+)"\)', 'reader.GetInt32(reader.GetOrdinal("$1"))'
        $content = $content -replace 'reader\.GetString\("([^"]+)"\)', 'reader.GetString(reader.GetOrdinal("$1"))'
        $content = $content -replace 'reader\.GetBoolean\("([^"]+)"\)', 'reader.GetBoolean(reader.GetOrdinal("$1"))'
        $content = $content -replace 'reader\.GetDateTime\("([^"]+)"\)', 'reader.GetDateTime(reader.GetOrdinal("$1"))'
        $content = $content -replace 'reader\.GetDecimal\("([^"]+)"\)', 'reader.GetDecimal(reader.GetOrdinal("$1"))'
        
        # Fix double GetOrdinal calls (in case they already exist)
        $content = $content -replace 'reader\.GetOrdinal\(reader\.GetOrdinal\("([^"]+)"\)\)', 'reader.GetOrdinal("$1")'
        
        Set-Content $file $content -NoNewline
        Write-Host "Fixed $file"
    }
}

Write-Host "Reader methods fixed!"
