using Microsoft.Data.Sqlite;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DebtManager.Data;
using DebtManager.Models;

namespace DebtManager.Services
{
    public class ClientService
    {
        public async Task<List<Client>> GetAllClientsAsync()
        {
            var clients = new List<Client>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();

            var query = @"SELECT Id, Name, Phone, Address, Notes, IsActive, CreatedDate
                         FROM Clients WHERE IsActive = 1 ORDER BY Name";

            using var command = new SqliteCommand(query, connection);
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                clients.Add(new Client
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Phone = reader.IsDBNull(reader.GetOrdinal("Phone")) ? null : reader.GetString(reader.GetOrdinal("Phone")),
                    Address = reader.IsDBNull(reader.GetOrdinal("Address")) ? null : reader.GetString(reader.GetOrdinal("Address")),
                    Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                });
            }
            
            return clients;
        }

        public async Task<Client?> GetClientByIdAsync(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();

            var query = @"SELECT Id, Name, Phone, Address, Notes, IsActive, CreatedDate
                         FROM Clients WHERE Id = @id";

            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            using var reader = await command.ExecuteReaderAsync();
            
            if (await reader.ReadAsync())
            {
                return new Client
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Phone = reader.IsDBNull(reader.GetOrdinal("Phone")) ? null : reader.GetString(reader.GetOrdinal("Phone")),
                    Address = reader.IsDBNull(reader.GetOrdinal("Address")) ? null : reader.GetString(reader.GetOrdinal("Address")),
                    Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                };
            }
            
            return null;
        }

        public async Task<int> AddClientAsync(Client client)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();

            var query = @"INSERT INTO Clients (Name, Phone, Address, Notes, IsActive, CreatedDate)
                         VALUES (@name, @phone, @address, @notes, @isActive, @createdDate);
                         SELECT last_insert_rowid();";

            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@name", client.Name);
            command.Parameters.AddWithValue("@phone", client.Phone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@address", client.Address ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@notes", client.Notes ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@isActive", client.IsActive);
            command.Parameters.AddWithValue("@createdDate", client.CreatedDate);
            
            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        public async Task<bool> UpdateClientAsync(Client client)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();

            var query = @"UPDATE Clients
                         SET Name = @name, Phone = @phone, Address = @address,
                             Notes = @notes, IsActive = @isActive
                         WHERE Id = @id";

            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", client.Id);
            command.Parameters.AddWithValue("@name", client.Name);
            command.Parameters.AddWithValue("@phone", client.Phone ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@address", client.Address ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@notes", client.Notes ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@isActive", client.IsActive);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteClientAsync(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();

            var query = "UPDATE Clients SET IsActive = 0 WHERE Id = @id";

            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        public async Task<List<Client>> SearchClientsAsync(string searchTerm)
        {
            var clients = new List<Client>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();

            var query = @"SELECT Id, Name, Phone, Address, Notes, IsActive, CreatedDate
                         FROM Clients
                         WHERE IsActive = 1 AND (Name LIKE @search OR Phone LIKE @search)
                         ORDER BY Name";

            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@search", $"%{searchTerm}%");
            
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                clients.Add(new Client
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Phone = reader.IsDBNull(reader.GetOrdinal("Phone")) ? null : reader.GetString(reader.GetOrdinal("Phone")),
                    Address = reader.IsDBNull(reader.GetOrdinal("Address")) ? null : reader.GetString(reader.GetOrdinal("Address")),
                    Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                });
            }
            
            return clients;
        }
    }
}
