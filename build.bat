@echo off
echo ========================================
echo Building DebtManager Project...
echo ========================================

echo Step 1: Cleaning previous build...
dotnet clean DebtManager.sln --verbosity quiet

echo Step 2: Restoring packages...
dotnet restore DebtManager.sln --verbosity quiet

echo Step 3: Building project...
dotnet build DebtManager.sln --verbosity minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ Build successful!
    echo ========================================
    echo You can now run the application with:
    echo   - Double-click run.bat
    echo   - Or use: dotnet run
    echo.
) else (
    echo.
    echo ========================================
    echo ❌ Build failed!
    echo ========================================
    echo Please check the error messages above.
    echo.
)
pause
