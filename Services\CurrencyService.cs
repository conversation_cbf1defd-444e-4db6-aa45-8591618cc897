using Microsoft.Data.Sqlite;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DebtManager.Data;
using DebtManager.Models;

namespace DebtManager.Services
{
    public class CurrencyService
    {
        public async Task<List<Currency>> GetAllCurrenciesAsync()
        {
            var currencies = new List<Currency>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT Id, Name, Code, Symbol, IsGold, IsActive, CreatedDate 
                         FROM Currencies WHERE IsActive = 1 ORDER BY Name";
            
            using var command = new SqliteCommand(query, connection);
            using var reader = await command.ExecuteReaderAsync();
            
            while (await reader.ReadAsync())
            {
                currencies.Add(new Currency
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Code = reader.GetString(reader.GetOrdinal("Code")),
                    Symbol = reader.GetString(reader.GetOrdinal("Symbol")),
                    IsGold = reader.GetBoolean(reader.GetOrdinal("IsGold")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                });
            }
            
            return currencies;
        }

        public async Task<Currency?> GetCurrencyByIdAsync(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"SELECT Id, Name, Code, Symbol, IsGold, IsActive, CreatedDate 
                         FROM Currencies WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            using var reader = await command.ExecuteReaderAsync();
            
            if (await reader.ReadAsync())
            {
                return new Currency
                {
                    Id = reader.GetInt32(reader.GetOrdinal("Id")),
                    Name = reader.GetString(reader.GetOrdinal("Name")),
                    Code = reader.GetString(reader.GetOrdinal("Code")),
                    Symbol = reader.GetString(reader.GetOrdinal("Symbol")),
                    IsGold = reader.GetBoolean(reader.GetOrdinal("IsGold")),
                    IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive")),
                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                };
            }
            
            return null;
        }

        public async Task<int> AddCurrencyAsync(Currency currency)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"INSERT INTO Currencies (Name, Code, Symbol, IsGold, IsActive, CreatedDate) 
                         VALUES (@name, @code, @symbol, @isGold, @isActive, @createdDate);
                         SELECT last_insert_rowid();";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@name", currency.Name);
            command.Parameters.AddWithValue("@code", currency.Code);
            command.Parameters.AddWithValue("@symbol", currency.Symbol);
            command.Parameters.AddWithValue("@isGold", currency.IsGold);
            command.Parameters.AddWithValue("@isActive", currency.IsActive);
            command.Parameters.AddWithValue("@createdDate", currency.CreatedDate);
            
            var result = await command.ExecuteScalarAsync();
            return Convert.ToInt32(result);
        }

        public async Task<bool> UpdateCurrencyAsync(Currency currency)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = @"UPDATE Currencies 
                         SET Name = @name, Code = @code, Symbol = @symbol, 
                             IsGold = @isGold, IsActive = @isActive 
                         WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", currency.Id);
            command.Parameters.AddWithValue("@name", currency.Name);
            command.Parameters.AddWithValue("@code", currency.Code);
            command.Parameters.AddWithValue("@symbol", currency.Symbol);
            command.Parameters.AddWithValue("@isGold", currency.IsGold);
            command.Parameters.AddWithValue("@isActive", currency.IsActive);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        public async Task<bool> DeleteCurrencyAsync(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = "UPDATE Currencies SET IsActive = 0 WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            var rowsAffected = await command.ExecuteNonQueryAsync();
            return rowsAffected > 0;
        }

        public async Task<bool> IsCurrencyCodeExistsAsync(string code, int? excludeId = null)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            await connection.OpenAsync();
            
            var query = "SELECT COUNT(*) FROM Currencies WHERE Code = @code AND IsActive = 1";
            if (excludeId.HasValue)
            {
                query += " AND Id != @excludeId";
            }
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@code", code);
            if (excludeId.HasValue)
            {
                command.Parameters.AddWithValue("@excludeId", excludeId.Value);
            }
            
            var count = Convert.ToInt32(await command.ExecuteScalarAsync());
            return count > 0;
        }
    }
}
