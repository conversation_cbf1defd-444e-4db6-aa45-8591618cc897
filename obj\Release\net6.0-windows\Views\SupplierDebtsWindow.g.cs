﻿#pragma checksum "..\..\..\..\Views\SupplierDebtsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "47FE32723BAE3967EBF99B68DA2C214EE00B8094"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Views {
    
    
    /// <summary>
    /// SupplierDebtsWindow
    /// </summary>
    public partial class SupplierDebtsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 34 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderText;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierNameText;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierCompanyText;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupplierPhoneText;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalWeOweText;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSupplierOwesText;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NetBalanceText;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DebtsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDebtButton;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MarkPaidButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/views/supplierdebtswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 47 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SupplierNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SupplierCompanyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.SupplierPhoneText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TotalWeOweText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TotalSupplierOwesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.NetBalanceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.DebtsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 156 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
            this.DebtsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DebtsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AddDebtButton = ((System.Windows.Controls.Button)(target));
            
            #line 194 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
            this.AddDebtButton.Click += new System.Windows.RoutedEventHandler(this.AddDebtButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.MarkPaidButton = ((System.Windows.Controls.Button)(target));
            
            #line 203 "..\..\..\..\Views\SupplierDebtsWindow.xaml"
            this.MarkPaidButton.Click += new System.Windows.RoutedEventHandler(this.MarkPaidButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

