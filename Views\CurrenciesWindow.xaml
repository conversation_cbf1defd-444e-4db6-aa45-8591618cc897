<Window x:Class="DebtManager.Views.CurrenciesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إدارة العملات" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CurrencyUsd" 
                                           Width="32" Height="32" 
                                           Foreground="White" 
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="إدارة العملات" 
                             FontSize="24" FontWeight="Bold" 
                             Foreground="White" 
                             VerticalAlignment="Center" 
                             Margin="10,0,0,0"/>
                </StackPanel>
                
                <Button Grid.Column="1" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Background="White"
                        BorderBrush="White"
                        Foreground="{DynamicResource PrimaryHueMidBrush}"
                        Click="CloseButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                        <TextBlock Text="إغلاق" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>
        
        <!-- Toolbar -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="20,20,20,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="AddCurrencyButton" 
                        Style="{StaticResource PrimaryButton}"
                        Click="AddCurrencyButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Width="20" Height="20"/>
                        <TextBlock Text="إضافة عملة" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>
                
                <Button x:Name="EditCurrencyButton" 
                        Style="{StaticResource SecondaryButton}"
                        Click="EditCurrencyButton_Click"
                        IsEnabled="False">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Edit" Width="20" Height="20"/>
                        <TextBlock Text="تعديل" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>
                
                <Button x:Name="DeleteCurrencyButton" 
                        Style="{StaticResource SecondaryButton}"
                        Click="DeleteCurrencyButton_Click"
                        IsEnabled="False"
                        Foreground="{StaticResource DebtColor}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Delete" Width="20" Height="20"/>
                        <TextBlock Text="حذف" Margin="8,0,0,0"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Border>
        
        <!-- Currencies DataGrid -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="20,0,20,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" 
                         Text="قائمة العملات" 
                         FontSize="18" FontWeight="Bold" 
                         HorizontalAlignment="Center" 
                         Margin="0,0,0,10"/>
                
                <DataGrid x:Name="CurrenciesDataGrid" 
                        Grid.Row="1"
                        Style="{StaticResource ModernDataGrid}"
                        SelectionChanged="CurrenciesDataGrid_SelectionChanged"
                        MouseDoubleClick="CurrenciesDataGrid_MouseDoubleClick">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرقم" 
                                          Binding="{Binding Id}" 
                                          Width="80"/>
                        <DataGridTextColumn Header="اسم العملة" 
                                          Binding="{Binding Name}" 
                                          Width="200"/>
                        <DataGridTextColumn Header="الرمز" 
                                          Binding="{Binding Code}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="الرمز المختصر" 
                                          Binding="{Binding Symbol}" 
                                          Width="120"/>
                        <DataGridCheckBoxColumn Header="ذهب" 
                                              Binding="{Binding IsGold}" 
                                              Width="80"/>
                        <DataGridTextColumn Header="تاريخ الإضافة"
                                          Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy HH:mm}"
                                          Width="140"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</Window>
