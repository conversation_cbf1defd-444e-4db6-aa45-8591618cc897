using System;
using System.ComponentModel.DataAnnotations;

using System.Collections.Generic;

namespace DebtManager.Models
{
    public class Currency
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(10)]
        public string Code { get; set; } = string.Empty;
        
        [StringLength(10)]
        public string Symbol { get; set; } = string.Empty;
        
        public bool IsGold { get; set; } = false;
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // Navigation property
        public virtual ICollection<Debt> Debts { get; set; } = new List<Debt>();
        
        public override string ToString()
        {
            return $"{Name} ({Symbol})";
        }
    }
}
