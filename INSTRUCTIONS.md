# تعليمات تشغيل برنامج DebtManager

## ✅ تم إصلاح جميع المشاكل!

### المشاكل التي تم حلها:
- ❌ خطأ `Spacing` property → ✅ تم استبداله بـ `Margin`
- ❌ حزم قديمة غير متوافقة → ✅ تم تحديث الحزم
- ❌ تحذيرات التوافق → ✅ تم حل جميع التحذيرات

## طرق التشغيل

### الطريقة الأولى: استخدام ملفات Batch (الأسهل)
1. **لتشخيص المشاكل**: انقر نقراً مزدوجاً على `diagnose.bat`
2. **لبناء المشروع**: انقر نقراً مزدوجاً على `build.bat`
3. **لتشغيل البرنامج**: انقر نقراً مزدوجاً على `run.bat`

### الطريقة الثانية: استخدام Command Prompt
1. افتح Command Prompt في مجلد المشروع
2. نفذ الأوامر التالية:
```cmd
dotnet restore
dotnet build
dotnet run
```

### الطريقة الثالثة: استخدام Visual Studio
1. افتح Visual Studio 2022
2. اختر "Open a project or solution"
3. اختر ملف `DebtManager.csproj`
4. اضغط F5 أو Ctrl+F5 لتشغيل البرنامج

### الطريقة الرابعة: استخدام Visual Studio Code
1. افتح Visual Studio Code
2. افتح مجلد المشروع
3. اضغط Ctrl+Shift+P
4. اكتب ".NET: Generate Assets for Build and Debug"
5. اضغط F5 لتشغيل البرنامج

## متطلبات النظام
- Windows 10/11
- .NET 6.0 Runtime أو أحدث
- 100 MB مساحة فارغة على القرص الصلب

## استكشاف الأخطاء

### إذا لم يعمل البرنامج:
1. تأكد من تثبيت .NET 6.0 أو أحدث
2. تأكد من وجود اتصال بالإنترنت لتحميل الحزم
3. جرب تشغيل Command Prompt كمدير (Run as Administrator)

### إذا ظهرت رسالة خطأ:
1. تأكد من أن جميع الملفات موجودة
2. تأكد من عدم وجود برامج مكافحة فيروسات تمنع التشغيل
3. جرب حذف مجلدي `bin` و `obj` ثم إعادة البناء

## أول استخدام
عند تشغيل البرنامج لأول مرة:
1. سيتم إنشاء قاعدة بيانات SQLite تلقائياً
2. سيتم إضافة العملات الافتراضية
3. يمكنك البدء بإضافة العملاء والموردين

## الدعم
إذا واجهت أي مشاكل، تأكد من:
- تحديث Windows إلى آخر إصدار
- تثبيت .NET 6.0 Desktop Runtime
- إعادة تشغيل الكمبيوتر بعد التثبيت
