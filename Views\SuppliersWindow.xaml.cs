using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class SuppliersWindow : Window
    {
        private readonly SupplierService _supplierService;
        private List<Supplier> _allSuppliers = new();

        public SuppliersWindow()
        {
            InitializeComponent();
            _supplierService = new SupplierService();
            Loaded += SuppliersWindow_Loaded;
        }

        private async void SuppliersWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadSuppliersAsync();
        }

        private async Task LoadSuppliersAsync()
        {
            try
            {
                _allSuppliers = await _supplierService.GetAllSuppliersAsync();
                SuppliersDataGrid.ItemsSource = _allSuppliers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchTerm = SearchTextBox.Text?.Trim();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                SuppliersDataGrid.ItemsSource = _allSuppliers;
            }
            else
            {
                try
                {
                    var filteredSuppliers = await _supplierService.SearchSuppliersAsync(searchTerm);
                    SuppliersDataGrid.ItemsSource = filteredSuppliers;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void SuppliersDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var hasSelection = SuppliersDataGrid.SelectedItem != null;
            EditSupplierButton.IsEnabled = hasSelection;
            ViewDebtsButton.IsEnabled = hasSelection;
            DeleteSupplierButton.IsEnabled = hasSelection;
        }

        private void SuppliersDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier selectedSupplier)
            {
                EditSupplier(selectedSupplier);
            }
        }

        private void AddSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var supplierDialog = new SupplierDialog();
            if (supplierDialog.ShowDialog() == true)
            {
                _ = LoadSuppliersAsync();
            }
        }

        private void EditSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier selectedSupplier)
            {
                EditSupplier(selectedSupplier);
            }
        }

        private void EditSupplier(Supplier supplier)
        {
            var supplierDialog = new SupplierDialog(supplier);
            if (supplierDialog.ShowDialog() == true)
            {
                _ = LoadSuppliersAsync();
            }
        }

        private void ViewDebtsButton_Click(object sender, RoutedEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier selectedSupplier)
            {
                var debtsWindow = new SupplierDebtsWindow(selectedSupplier);
                debtsWindow.ShowDialog();
            }
        }

        private async void DeleteSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier selectedSupplier)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المورد '{selectedSupplier.Name}'؟\nسيتم إلغاء تفعيل المورد وليس حذفه نهائياً.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var success = await _supplierService.DeleteSupplierAsync(selectedSupplier.Id);
                        if (success)
                        {
                            MessageBox.Show("تم حذف المورد بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                            await LoadSuppliersAsync();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المورد", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void BackButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
