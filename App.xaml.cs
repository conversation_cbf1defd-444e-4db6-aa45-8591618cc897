using System.Windows;
using DebtManager.Data;
using DebtManager.Services;

namespace DebtManager
{
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            // Initialize database
            DatabaseService.InitializeDatabase();
            
            // Set culture for Arabic support
            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("ar-SA");
            System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("ar-SA");
        }
    }
}
