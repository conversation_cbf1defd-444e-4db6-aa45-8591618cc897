using System;
using System.ComponentModel.DataAnnotations;

namespace DebtManager.Models
{
    public enum DebtType
    {
        ClientOwesUs = 1,      // العميل مدين لنا (أخضر)
        WeOweClient = 2,       // نحن مدينون للعميل (أحمر)
        WeOweSupplier = 3,     // نحن مدينون للمورد (أحمر)
        SupplierOwesUs = 4     // المورد مدين لنا (أخضر)
    }

    public enum TransactionType
    {
        Debit = 1,    // سحب
        Credit = 2    // دفع
    }

    public class Debt
    {
        public int Id { get; set; }
        
        public int? ClientId { get; set; }
        public virtual Client? Client { get; set; }
        
        public int? SupplierId { get; set; }
        public virtual Supplier? Supplier { get; set; }
        
        [Required]
        public int CurrencyId { get; set; }
        public virtual Currency Currency { get; set; } = null!;
        
        [Required]
        public decimal Amount { get; set; }
        
        [Required]
        public DebtType DebtType { get; set; }
        
        [Required]
        public TransactionType TransactionType { get; set; }
        
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        
        public DateTime? DueDate { get; set; }
        
        [StringLength(500)]
        public string? Notes { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public bool IsPaid { get; set; } = false;
        
        public DateTime? PaidDate { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        [StringLength(50)]
        public string? CreatedBy { get; set; }
        
        // Calculated properties
        public string PersonName => Client?.Name ?? Supplier?.Name ?? "غير محدد";
        
        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.Today && !IsPaid;
        
        public int DaysUntilDue => DueDate.HasValue ? (DueDate.Value - DateTime.Today).Days : 0;
        
        public string StatusText => IsPaid ? "مدفوع" : IsOverdue ? "متأخر" : DaysUntilDue <= 7 ? "مستحق قريباً" : "نشط";
        
        public string DebtTypeText => DebtType switch
        {
            DebtType.ClientOwesUs => "العميل مدين لنا",
            DebtType.WeOweClient => "نحن مدينون للعميل",
            DebtType.WeOweSupplier => "نحن مدينون للمورد",
            DebtType.SupplierOwesUs => "المورد مدين لنا",
            _ => "غير محدد"
        };
        
        public string TransactionTypeText => TransactionType switch
        {
            TransactionType.Debit => "سحب",
            TransactionType.Credit => "دفع",
            _ => "غير محدد"
        };
    }
}
