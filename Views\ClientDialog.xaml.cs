using System;
using System.Windows;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class ClientDialog : Window
    {
        private readonly ClientService _clientService;
        private readonly Client? _existingClient;
        private readonly bool _isEditMode;

        public ClientDialog(Client? client = null)
        {
            InitializeComponent();
            _clientService = new ClientService();
            _existingClient = client;
            _isEditMode = client != null;
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            if (_isEditMode && _existingClient != null)
            {
                HeaderText.Text = "تعديل بيانات العميل";
                Title = "تعديل عميل";
                
                // Fill form with existing data
                NameTextBox.Text = _existingClient.Name;
                PhoneTextBox.Text = _existingClient.Phone ?? string.Empty;
                AddressTextBox.Text = _existingClient.Address ?? string.Empty;
                NotesTextBox.Text = _existingClient.Notes ?? string.Empty;
            }
            else
            {
                HeaderText.Text = "إضافة عميل جديد";
                Title = "إضافة عميل";
            }
            
            // Focus on name field
            NameTextBox.Focus();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                SaveButton.IsEnabled = false;
                
                var client = new Client
                {
                    Name = NameTextBox.Text.Trim(),
                    Phone = string.IsNullOrWhiteSpace(PhoneTextBox.Text) ? null : PhoneTextBox.Text.Trim(),
                    Address = string.IsNullOrWhiteSpace(AddressTextBox.Text) ? null : AddressTextBox.Text.Trim(),
                    Notes = string.IsNullOrWhiteSpace(NotesTextBox.Text) ? null : NotesTextBox.Text.Trim()
                };

                bool success;
                if (_isEditMode && _existingClient != null)
                {
                    client.Id = _existingClient.Id;
                    client.CreatedDate = _existingClient.CreatedDate;
                    client.IsActive = _existingClient.IsActive;
                    success = await _clientService.UpdateClientAsync(client);
                }
                else
                {
                    var newId = await _clientService.AddClientAsync(client);
                    success = newId > 0;
                }

                if (success)
                {
                    var message = _isEditMode ? "تم تحديث بيانات العميل بنجاح" : "تم إضافة العميل بنجاح";
                    MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    var message = _isEditMode ? "فشل في تحديث بيانات العميل" : "فشل في إضافة العميل";
                    MessageBox.Show(message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                var action = _isEditMode ? "تحديث" : "إضافة";
                MessageBox.Show($"خطأ في {action} العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }

        private bool ValidateForm()
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            // Validate name length
            if (NameTextBox.Text.Trim().Length < 2)
            {
                MessageBox.Show("يجب أن يكون اسم العميل أكثر من حرف واحد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            // Validate phone format if provided
            if (!string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                var phone = PhoneTextBox.Text.Trim();
                if (phone.Length < 7 || phone.Length > 20)
                {
                    MessageBox.Show("رقم الهاتف يجب أن يكون بين 7 و 20 رقم", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    PhoneTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
