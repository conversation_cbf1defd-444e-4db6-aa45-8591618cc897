﻿#pragma checksum "..\..\..\..\Views\CurrenciesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E444B7E67F3867E95CC386B11AE2A82080FA2963"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Views {
    
    
    /// <summary>
    /// CurrenciesWindow
    /// </summary>
    public partial class CurrenciesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 57 "..\..\..\..\Views\CurrenciesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddCurrencyButton;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Views\CurrenciesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditCurrencyButton;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\CurrenciesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteCurrencyButton;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\CurrenciesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CurrenciesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/views/currencieswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\CurrenciesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 45 "..\..\..\..\Views\CurrenciesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AddCurrencyButton = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Views\CurrenciesWindow.xaml"
            this.AddCurrencyButton.Click += new System.Windows.RoutedEventHandler(this.AddCurrencyButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.EditCurrencyButton = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\..\Views\CurrenciesWindow.xaml"
            this.EditCurrencyButton.Click += new System.Windows.RoutedEventHandler(this.EditCurrencyButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DeleteCurrencyButton = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\Views\CurrenciesWindow.xaml"
            this.DeleteCurrencyButton.Click += new System.Windows.RoutedEventHandler(this.DeleteCurrencyButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CurrenciesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 106 "..\..\..\..\Views\CurrenciesWindow.xaml"
            this.CurrenciesDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CurrenciesDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 107 "..\..\..\..\Views\CurrenciesWindow.xaml"
            this.CurrenciesDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.CurrenciesDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

