@echo off
echo ========================================
echo DebtManager Project Diagnostics
echo ========================================
echo.

echo Checking .NET installation...
dotnet --version
echo.

echo Checking .NET SDKs...
dotnet --list-sdks
echo.

echo Checking .NET Runtimes...
dotnet --list-runtimes
echo.

echo Checking project file...
if exist "DebtManager.csproj" (
    echo ✅ DebtManager.csproj found
) else (
    echo ❌ DebtManager.csproj not found
)
echo.

echo Checking key files...
if exist "App.xaml" echo ✅ App.xaml found
if exist "Views\MainWindow.xaml" echo ✅ MainWindow.xaml found
if exist "Models\Client.cs" echo ✅ Client.cs found
if exist "Services\ClientService.cs" echo ✅ ClientService.cs found
echo.

echo Attempting to restore packages...
dotnet restore --verbosity detailed

echo.
echo ========================================
echo Diagnostics complete
echo ========================================
pause
