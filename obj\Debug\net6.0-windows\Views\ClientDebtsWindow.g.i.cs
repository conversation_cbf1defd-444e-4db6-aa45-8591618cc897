﻿#pragma checksum "..\..\..\..\Views\ClientDebtsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "46B21ECEECC50EDE660A4B75FED67DAC28CE1409"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DebtManager.Views {
    
    
    /// <summary>
    /// ClientDebtsWindow
    /// </summary>
    public partial class ClientDebtsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 34 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderText;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClientNameText;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClientPhoneText;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalDebtText;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCreditText;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NetBalanceText;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DebtsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddDebtButton;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\ClientDebtsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MarkPaidButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DebtManager;V1.0.0.0;component/views/clientdebtswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ClientDebtsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 47 "..\..\..\..\Views\ClientDebtsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ClientNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ClientPhoneText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TotalDebtText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.TotalCreditText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.NetBalanceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.DebtsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 154 "..\..\..\..\Views\ClientDebtsWindow.xaml"
            this.DebtsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DebtsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AddDebtButton = ((System.Windows.Controls.Button)(target));
            
            #line 192 "..\..\..\..\Views\ClientDebtsWindow.xaml"
            this.AddDebtButton.Click += new System.Windows.RoutedEventHandler(this.AddDebtButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.MarkPaidButton = ((System.Windows.Controls.Button)(target));
            
            #line 201 "..\..\..\..\Views\ClientDebtsWindow.xaml"
            this.MarkPaidButton.Click += new System.Windows.RoutedEventHandler(this.MarkPaidButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

