<Window x:Class="DebtManager.Views.SearchWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="البحث المتقدم" 
        Height="600" Width="1000"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        ResizeMode="CanResize"
        MinHeight="500" MinWidth="800"
        Loaded="SearchWindow_Loaded">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="Magnify" 
                                   Width="32" Height="32" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   VerticalAlignment="Center"/>
            <TextBlock Text="البحث المتقدم في المعاملات" 
                     FontSize="24" FontWeight="Bold" 
                     Foreground="{DynamicResource PrimaryHueMidBrush}" 
                     VerticalAlignment="Center" 
                     Margin="10,0,0,0"/>
        </StackPanel>
        
        <!-- Search Filters -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <StackPanel>
                    <TextBlock Text="فلاتر البحث" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                    
                    <!-- First Row -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Search Text -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="البحث في النص" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="SearchTextBox"
                                   materialDesign:HintAssist.Hint="اسم الشخص، الملاحظات، إلخ"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   TextChanged="SearchTextBox_TextChanged"/>
                        </StackPanel>
                        
                        <!-- Amount From -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="المبلغ من" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="AmountFromTextBox"
                                   materialDesign:HintAssist.Hint="الحد الأدنى للمبلغ"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   PreviewTextInput="AmountTextBox_PreviewTextInput"/>
                        </StackPanel>
                        
                        <!-- Amount To -->
                        <StackPanel Grid.Column="4">
                            <TextBlock Text="المبلغ إلى" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="AmountToTextBox"
                                   materialDesign:HintAssist.Hint="الحد الأقصى للمبلغ"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   PreviewTextInput="AmountTextBox_PreviewTextInput"/>
                        </StackPanel>
                    </Grid>
                    
                    <!-- Second Row -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Date From -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="من تاريخ" FontWeight="Bold" Margin="0,0,0,5"/>
                            <DatePicker x:Name="DateFromPicker"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                        </StackPanel>
                        
                        <!-- Date To -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="إلى تاريخ" FontWeight="Bold" Margin="0,0,0,5"/>
                            <DatePicker x:Name="DateToPicker"
                                      Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                        </StackPanel>
                        
                        <!-- Currency -->
                        <StackPanel Grid.Column="4">
                            <TextBlock Text="العملة" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CurrencyComboBox"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    DisplayMemberPath="Name"/>
                        </StackPanel>
                    </Grid>
                    
                    <!-- Third Row -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Debt Type -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="نوع الدين" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="DebtTypeComboBox"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                <ComboBoxItem Content="جميع الأنواع" Tag="All" IsSelected="True"/>
                                <ComboBoxItem Content="العميل مدين لنا" Tag="ClientOwesUs"/>
                                <ComboBoxItem Content="نحن مدينون للعميل" Tag="WeOweClient"/>
                                <ComboBoxItem Content="المورد مدين لنا" Tag="SupplierOwesUs"/>
                                <ComboBoxItem Content="نحن مدينون للمورد" Tag="WeOweSupplier"/>
                            </ComboBox>
                        </StackPanel>
                        
                        <!-- Status -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="الحالة" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="StatusComboBox"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                <ComboBoxItem Content="جميع الحالات" Tag="All" IsSelected="True"/>
                                <ComboBoxItem Content="نشط" Tag="Active"/>
                                <ComboBoxItem Content="مدفوع" Tag="Paid"/>
                                <ComboBoxItem Content="متأخر" Tag="Overdue"/>
                                <ComboBoxItem Content="مستحق قريباً" Tag="DueSoon"/>
                            </ComboBox>
                        </StackPanel>
                        
                        <!-- Person Type -->
                        <StackPanel Grid.Column="4">
                            <TextBlock Text="نوع الشخص" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="PersonTypeComboBox"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                <ComboBoxItem Content="الكل" Tag="All" IsSelected="True"/>
                                <ComboBoxItem Content="العملاء" Tag="Clients"/>
                                <ComboBoxItem Content="الموردين" Tag="Suppliers"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>
                    
                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                        <Button x:Name="SearchButton" 
                                Style="{StaticResource PrimaryButton}"
                                Click="SearchButton_Click"
                                Width="120" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20"/>
                                <TextBlock Text="بحث" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="ClearButton" 
                                Style="{StaticResource SecondaryButton}"
                                Click="ClearButton_Click"
                                Width="120" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                                <TextBlock Text="مسح الفلاتر" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="SaveFilterButton"
                                Style="{StaticResource SecondaryButton}"
                                Click="SaveFilterButton_Click"
                                Width="120">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20"/>
                                <TextBlock Text="حفظ الفلتر" Margin="8,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- Results -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Results Header -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="نتائج البحث" FontSize="16" FontWeight="Bold"/>
                    <TextBlock x:Name="ResultsCountText" Text="(0 نتيجة)" FontSize="14" 
                             Foreground="Gray" Margin="10,0,0,0"/>
                </StackPanel>
                
                <!-- Results Grid -->
                <DataGrid x:Name="ResultsDataGrid" 
                        Grid.Row="1"
                        Style="{StaticResource ModernDataGrid}"
                        MouseDoubleClick="ResultsDataGrid_MouseDoubleClick">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ والوقت" 
                                          Binding="{Binding TransactionDate, StringFormat=dd/MM/yyyy HH:mm}" 
                                          Width="140"/>
                        <DataGridTextColumn Header="الشخص" Binding="{Binding PersonName}" Width="150"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="100"/>
                        <DataGridTextColumn Header="العملة" Binding="{Binding Currency.Symbol}" Width="80"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding DebtTypeText}" Width="120"/>
                        <DataGridTextColumn Header="العملية" Binding="{Binding TransactionTypeText}" Width="80"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/>
                        <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
                
                <!-- Export Options -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <Button x:Name="ExportResultsButton" 
                            Style="{StaticResource SecondaryButton}"
                            Click="ExportResultsButton_Click"
                            Width="120">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Export" Width="20" Height="20"/>
                            <TextBlock Text="تصدير النتائج" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
