using System;
using System.Windows;
using DebtManager.Services;
using DebtManager.Models;

namespace DebtManager.Views
{
    public partial class SupplierDialog : Window
    {
        private readonly SupplierService _supplierService;
        private readonly Supplier? _existingSupplier;
        private readonly bool _isEditMode;

        public SupplierDialog(Supplier? supplier = null)
        {
            InitializeComponent();
            _supplierService = new SupplierService();
            _existingSupplier = supplier;
            _isEditMode = supplier != null;
            
            InitializeForm();
        }

        private void InitializeForm()
        {
            if (_isEditMode && _existingSupplier != null)
            {
                HeaderText.Text = "تعديل بيانات المورد";
                Title = "تعديل مورد";
                
                // Fill form with existing data
                NameTextBox.Text = _existingSupplier.Name;
                CompanyTextBox.Text = _existingSupplier.Company ?? string.Empty;
                PhoneTextBox.Text = _existingSupplier.Phone ?? string.Empty;
                AddressTextBox.Text = _existingSupplier.Address ?? string.Empty;
                NotesTextBox.Text = _existingSupplier.Notes ?? string.Empty;
            }
            else
            {
                HeaderText.Text = "إضافة مورد جديد";
                Title = "إضافة مورد";
            }
            
            // Focus on name field
            NameTextBox.Focus();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                SaveButton.IsEnabled = false;
                
                var supplier = new Supplier
                {
                    Name = NameTextBox.Text.Trim(),
                    Company = string.IsNullOrWhiteSpace(CompanyTextBox.Text) ? null : CompanyTextBox.Text.Trim(),
                    Phone = string.IsNullOrWhiteSpace(PhoneTextBox.Text) ? null : PhoneTextBox.Text.Trim(),
                    Address = string.IsNullOrWhiteSpace(AddressTextBox.Text) ? null : AddressTextBox.Text.Trim(),
                    Notes = string.IsNullOrWhiteSpace(NotesTextBox.Text) ? null : NotesTextBox.Text.Trim()
                };

                bool success;
                if (_isEditMode && _existingSupplier != null)
                {
                    supplier.Id = _existingSupplier.Id;
                    supplier.CreatedDate = _existingSupplier.CreatedDate;
                    supplier.IsActive = _existingSupplier.IsActive;
                    success = await _supplierService.UpdateSupplierAsync(supplier);
                }
                else
                {
                    var newId = await _supplierService.AddSupplierAsync(supplier);
                    success = newId > 0;
                }

                if (success)
                {
                    var message = _isEditMode ? "تم تحديث بيانات المورد بنجاح" : "تم إضافة المورد بنجاح";
                    MessageBox.Show(message, "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    var message = _isEditMode ? "فشل في تحديث بيانات المورد" : "فشل في إضافة المورد";
                    MessageBox.Show(message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                var action = _isEditMode ? "تحديث" : "إضافة";
                MessageBox.Show($"خطأ في {action} المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
            }
        }

        private bool ValidateForm()
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المورد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            // Validate name length
            if (NameTextBox.Text.Trim().Length < 2)
            {
                MessageBox.Show("يجب أن يكون اسم المورد أكثر من حرف واحد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            // Validate phone format if provided
            if (!string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                var phone = PhoneTextBox.Text.Trim();
                if (phone.Length < 7 || phone.Length > 20)
                {
                    MessageBox.Show("رقم الهاتف يجب أن يكون بين 7 و 20 رقم", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    PhoneTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
