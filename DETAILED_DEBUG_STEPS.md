# 🔍 خطوات تشخيص مفصلة لمشكلة إضافة الدين

## ✅ التحسينات التي تمت:

### 1. معالجة أخطاء شاملة:
- إضافة try-catch في constructor الخاص بـ DebtWindow
- إضافة try-catch في DebtWindow_Loaded
- إضافة try-catch منفصل لكل خدمة (العملاء، الموردين، العملات)
- إضافة try-catch في MainWindow عند فتح DebtWindow

### 2. رسائل خطأ مفصلة:
- عرض StackTrace كامل لتحديد مكان المشكلة بالضبط
- رسائل منفصلة لكل نوع من البيانات

## 🧪 خطوات الاختبار:

### الخطوة 1: تشغيل التطبيق
```bash
dotnet run --project DebtManager.csproj
```

### الخطوة 2: اختبار فتح نافذة إضافة الدين
1. انقر على زر "إضافة معاملة جديدة"
2. **راقب ما يحدث:**
   - هل تظهر رسالة خطأ؟
   - هل تُفتح النافذة ثم تُغلق؟
   - هل يُغلق التطبيق كاملاً؟

### الخطوة 3: إذا ظهرت رسالة خطأ
- **اقرأ الرسالة بالكامل**
- **انسخ النص كاملاً** (خاصة جزء "تفاصيل")
- أرسل لي الرسالة كاملة

### الخطوة 4: إذا لم تظهر رسالة خطأ
- تحقق من وجود ملف `DebtManager.db` في مجلد التطبيق
- جرب فتح التطبيق من Command Prompt لرؤية أي رسائل:
  ```cmd
  cd "C:\Users\<USER>\Desktop\Y"
  dotnet run --project DebtManager.csproj
  ```

## 🔧 اختبارات إضافية:

### اختبار 1: تحقق من قاعدة البيانات
```cmd
dir DebtManager.db
```
يجب أن يظهر الملف. إذا لم يظهر، فالمشكلة في إنشاء قاعدة البيانات.

### اختبار 2: اختبار الخدمات منفرداً
1. جرب فتح "إدارة العملاء" - هل يعمل؟
2. جرب فتح "إدارة الموردين" - هل يعمل؟
3. جرب فتح "إدارة العملات" - هل يعمل؟

### اختبار 3: إضافة بيانات أساسية
1. أضف عملة واحدة على الأقل (مثل: دولار، $)
2. أضف عميل واحد على الأقل
3. ثم جرب إضافة الدين

## 📝 معلومات مطلوبة:

إذا استمرت المشكلة، أرسل لي:

### 1. رسالة الخطأ الكاملة (إن وجدت)
```
نص رسالة الخطأ كاملة مع التفاصيل
```

### 2. في أي خطوة تحديداً تحدث المشكلة؟
- [ ] عند النقر على زر "إضافة معاملة جديدة"
- [ ] عند فتح النافذة
- [ ] عند تحميل البيانات
- [ ] عند ملء النموذج
- [ ] عند النقر على "حفظ"

### 3. حالة الملفات:
- [ ] هل يوجد ملف `DebtManager.db`؟
- [ ] هل توجد بيانات في العملات/العملاء/الموردين؟

### 4. سلوك التطبيق:
- [ ] يُغلق التطبيق كاملاً
- [ ] تُغلق النافذة فقط
- [ ] يتجمد التطبيق
- [ ] تظهر رسالة خطأ

## 🎯 الهدف:
الحصول على رسالة خطأ مفصلة تُظهر بالضبط أين المشكلة، بدلاً من إغلاق التطبيق بصمت.

---
**مع معالجة الأخطاء المحسنة، يجب أن نحصل على معلومات مفيدة! 🔍**
