<Window x:Class="DebtManager.Views.SupplierDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة/تعديل مورد"
        Height="650" Width="750"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        ResizeMode="CanResize"
        MinHeight="650" MinWidth="750">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="TruckDelivery" 
                                   Width="32" Height="32" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   VerticalAlignment="Center"/>
            <TextBlock x:Name="HeaderText"
                     Text="إضافة مورد جديد" 
                     FontSize="24" FontWeight="Bold" 
                     Foreground="{DynamicResource PrimaryHueMidBrush}" 
                     VerticalAlignment="Center" 
                     Margin="10,0,0,0"/>
        </StackPanel>
        
        <!-- Form -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="10">
                <StackPanel>
                <!-- Supplier Name -->
                <TextBox x:Name="NameTextBox"
                         materialDesign:HintAssist.Hint="اسم المورد *"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="100"
                         Margin="0,0,0,15"/>

                <!-- Company -->
                <TextBox x:Name="CompanyTextBox"
                         materialDesign:HintAssist.Hint="اسم الشركة"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="100"
                         Margin="0,0,0,15"/>

                <!-- Phone -->
                <TextBox x:Name="PhoneTextBox"
                         materialDesign:HintAssist.Hint="رقم الهاتف"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="20"
                         Margin="0,0,0,15"/>

                <!-- Address -->
                <TextBox x:Name="AddressTextBox"
                         materialDesign:HintAssist.Hint="العنوان"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="200"
                         Margin="0,0,0,15"/>

                <!-- Notes -->
                <TextBox x:Name="NotesTextBox"
                         materialDesign:HintAssist.Hint="ملاحظات"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         MaxLength="500"
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,15"/>

                <!-- Required Field Note -->
                <TextBlock Text="* الحقول المطلوبة"
                         FontSize="12"
                         Foreground="Gray"
                         HorizontalAlignment="Right"/>
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            <Button x:Name="SaveButton" 
                    Style="{StaticResource PrimaryButton}"
                    Click="SaveButton_Click"
                    Width="120">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ContentSave" Width="20" Height="20"/>
                    <TextBlock Text="حفظ" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
            
            <Button x:Name="CancelButton" 
                    Style="{StaticResource SecondaryButton}"
                    Click="CancelButton_Click"
                    Width="120">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cancel" Width="20" Height="20"/>
                    <TextBlock Text="إلغاء" Margin="8,0,0,0"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>
