<Window x:Class="DebtManager.Views.ReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="التقارير والإحصائيات" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundColor}"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        ResizeMode="CanResize"
        MinHeight="600" MinWidth="800"
        Loaded="ReportsWindow_Loaded">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="ChartLine" 
                                   Width="32" Height="32" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   VerticalAlignment="Center"/>
            <TextBlock Text="التقارير والإحصائيات" 
                     FontSize="24" FontWeight="Bold" 
                     Foreground="{DynamicResource PrimaryHueMidBrush}" 
                     VerticalAlignment="Center" 
                     Margin="10,0,0,0"/>
        </StackPanel>
        
        <!-- Filters -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="فلاتر التقرير" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Date From -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="من تاريخ" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="FromDatePicker"
                                  Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                    </StackPanel>
                    
                    <!-- Date To -->
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="إلى تاريخ" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="ToDatePicker"
                                  Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                    </StackPanel>
                    
                    <!-- Currency Filter -->
                    <StackPanel Grid.Column="4">
                        <TextBlock Text="العملة" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CurrencyFilterComboBox"
                                Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                DisplayMemberPath="Name"/>
                    </StackPanel>
                    
                    <!-- Generate Button -->
                    <Button x:Name="GenerateReportButton" 
                            Grid.Column="6"
                            Style="{StaticResource PrimaryButton}"
                            Click="GenerateReportButton_Click"
                            VerticalAlignment="Bottom"
                            Width="120">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileChart" Width="20" Height="20"/>
                            <TextBlock Text="إنشاء التقرير" Margin="8,0,0,0"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </StackPanel>
        </Border>
        
        <!-- Reports Content -->
        <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}">
            
            <!-- Summary Tab -->
            <TabItem Header="الملخص العام">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        
                        <!-- Summary Cards -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Total Debts Card -->
                            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Background="#E8F5E8">
                                <StackPanel HorizontalAlignment="Center" Margin="15">
                                    <materialDesign:PackIcon Kind="TrendingUp" Width="32" Height="32" 
                                                           Foreground="Green" HorizontalAlignment="Center"/>
                                    <TextBlock Text="إجمالي المستحقات لنا" FontWeight="Bold" 
                                             HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                    <TextBlock x:Name="TotalDebtsToUsText" Text="0.00" FontSize="18" 
                                             FontWeight="Bold" Foreground="Green" 
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Total Owed Card -->
                            <Border Grid.Column="2" Style="{StaticResource CardStyle}" Background="#FFE8E8">
                                <StackPanel HorizontalAlignment="Center" Margin="15">
                                    <materialDesign:PackIcon Kind="TrendingDown" Width="32" Height="32" 
                                                           Foreground="Red" HorizontalAlignment="Center"/>
                                    <TextBlock Text="إجمالي المستحقات علينا" FontWeight="Bold" 
                                             HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                    <TextBlock x:Name="TotalOwedByUsText" Text="0.00" FontSize="18" 
                                             FontWeight="Bold" Foreground="Red" 
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Net Balance Card -->
                            <Border Grid.Column="4" Style="{StaticResource CardStyle}" Background="#E8F0FF">
                                <StackPanel HorizontalAlignment="Center" Margin="15">
                                    <materialDesign:PackIcon Kind="ScaleBalance" Width="32" Height="32" 
                                                           Foreground="Blue" HorizontalAlignment="Center"/>
                                    <TextBlock Text="الرصيد الصافي" FontWeight="Bold" 
                                             HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                    <TextBlock x:Name="NetBalanceText" Text="0.00" FontSize="18" 
                                             FontWeight="Bold" Foreground="Blue" 
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Overdue Card -->
                            <Border Grid.Column="6" Style="{StaticResource CardStyle}" Background="#FFF0E8">
                                <StackPanel HorizontalAlignment="Center" Margin="15">
                                    <materialDesign:PackIcon Kind="AlertCircle" Width="32" Height="32" 
                                                           Foreground="Orange" HorizontalAlignment="Center"/>
                                    <TextBlock Text="الديون المتأخرة" FontWeight="Bold" 
                                             HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                    <TextBlock x:Name="OverdueDebtsText" Text="0.00" FontSize="18" 
                                             FontWeight="Bold" Foreground="Orange" 
                                             HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                        
                        <!-- Detailed Summary -->
                        <Border Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="تفاصيل الملخص حسب العملة" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                                <DataGrid x:Name="SummaryByCurrencyGrid" 
                                        Style="{StaticResource ModernDataGrid}"
                                        MaxHeight="300">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="العملة" Binding="{Binding CurrencyName}" Width="100"/>
                                        <DataGridTextColumn Header="المستحقات لنا" Binding="{Binding DebtsToUs, StringFormat=N2}" Width="120"/>
                                        <DataGridTextColumn Header="المستحقات علينا" Binding="{Binding OwedByUs, StringFormat=N2}" Width="120"/>
                                        <DataGridTextColumn Header="الرصيد الصافي" Binding="{Binding NetBalance, StringFormat=N2}" Width="120"/>
                                        <DataGridTextColumn Header="عدد المعاملات" Binding="{Binding TransactionCount}" Width="100"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- Detailed Report Tab -->
            <TabItem Header="التقرير المفصل">
                <Border Style="{StaticResource CardStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Text="تفاصيل جميع المعاملات" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        
                        <DataGrid x:Name="DetailedReportGrid" 
                                Grid.Row="1"
                                Style="{StaticResource ModernDataGrid}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="التاريخ والوقت" 
                                                  Binding="{Binding TransactionDate, StringFormat=dd/MM/yyyy HH:mm}" 
                                                  Width="140"/>
                                <DataGridTextColumn Header="الشخص" Binding="{Binding PersonName}" Width="150"/>
                                <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="100"/>
                                <DataGridTextColumn Header="العملة" Binding="{Binding Currency.Symbol}" Width="80"/>
                                <DataGridTextColumn Header="النوع" Binding="{Binding DebtTypeText}" Width="120"/>
                                <DataGridTextColumn Header="العملية" Binding="{Binding TransactionTypeText}" Width="80"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="100"/>
                                <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>
                        
                        <!-- Export Buttons -->
                        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                            <Button x:Name="ExportPdfButton" 
                                    Style="{StaticResource SecondaryButton}"
                                    Click="ExportPdfButton_Click"
                                    Width="120" Margin="0,0,10,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FilePdf" Width="20" Height="20"/>
                                    <TextBlock Text="تصدير PDF" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>
                            
                            <Button x:Name="ExportExcelButton" 
                                    Style="{StaticResource SecondaryButton}"
                                    Click="ExportExcelButton_Click"
                                    Width="120">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileExcel" Width="20" Height="20"/>
                                    <TextBlock Text="تصدير Excel" Margin="8,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </Grid>
                </Border>
            </TabItem>
            
            <!-- Charts Tab -->
            <TabItem Header="الرسوم البيانية">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <TextBlock Text="الرسوم البيانية" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock Text="سيتم إضافة الرسوم البيانية في التحديث القادم" 
                                 FontSize="14" Foreground="Gray" 
                                 HorizontalAlignment="Center" 
                                 VerticalAlignment="Center" 
                                 Margin="0,50,0,0"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </Grid>
</Window>
